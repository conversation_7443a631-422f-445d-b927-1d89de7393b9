# coding: utf-8

"""
    Manticore Search Client

    Сlient for Manticore Search. 

    The version of the OpenAPI document: 5.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from manticoresearch.models.hits_hits import HitsHits
from typing import Optional, Set
from typing_extensions import Self

class SearchResponseHits(BaseModel):
    """
    Object containing the search hits, which represent the documents that matched the query.
    """ # noqa: E501
    max_score: Optional[StrictInt] = Field(default=None, description="Maximum score among the matched documents")
    total: Optional[StrictInt] = Field(default=None, description="Total number of matched documents")
    total_relation: Optional[StrictStr] = Field(default=None, description="Indicates whether the total number of hits is accurate or an estimate")
    hits: Optional[List[HitsHits]] = Field(default=None, description="Array of hit objects, each representing a matched document")
    __properties: ClassVar[List[str]] = ["max_score", "total", "total_relation", "hits"]

    #model_config = ConfigDict(
    #    populate_by_name=True,
    #    validate_assignment=True,
    #    protected_namespaces=(),
    #)


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SearchResponseHits from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in hits (list)
        _items = []
        if self.hits:
            for _item_hits in self.hits:
                if _item_hits:
                    _items.append(_item_hits.to_dict())
            _dict['hits'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SearchResponseHits from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "max_score": obj.get("max_score"),
            "total": obj.get("total"),
            "total_relation": obj.get("total_relation"),
            "hits": [HitsHits.from_dict(_item) for _item in obj["hits"]] if obj.get("hits") is not None else None
        })
        return _obj


