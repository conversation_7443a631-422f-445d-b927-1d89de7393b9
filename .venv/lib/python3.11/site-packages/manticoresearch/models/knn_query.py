# coding: utf-8

"""
    Manticore Search Client

    Сlient for Manticore Search. 

    The version of the OpenAPI document: 5.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from manticoresearch.models.query_filter import QueryFilter
from typing import Optional, Set
from typing_extensions import Self

class KnnQuery(BaseModel):
    """
    Object representing a k-nearest neighbor search query
    """ # noqa: E501
    var_field: StrictStr = Field(description="Field to perform the k-nearest neighbor search on", alias="field")
    k: StrictInt = Field(description="The number of nearest neighbors to return")
    query_vector: Optional[List[Union[StrictFloat, StrictInt]]] = Field(default=None, description="The vector used as input for the KNN search")
    doc_id: Optional[StrictInt] = Field(default=None, description="The docuemnt ID used as input for the KNN search")
    ef: Optional[StrictInt] = Field(default=None, description="Optional parameter controlling the accuracy of the search")
    filter: Optional[QueryFilter] = None
    __properties: ClassVar[List[str]] = ["field", "k", "query_vector", "doc_id", "ef", "filter"]

    #model_config = ConfigDict(
    #    populate_by_name=True,
    #    validate_assignment=True,
    #    protected_namespaces=(),
    #)


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of KnnQuery from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of filter
        if self.filter:
            _dict['filter'] = self.filter.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of KnnQuery from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "field": obj.get("field"),
            "k": obj.get("k"),
            "query_vector": obj.get("query_vector"),
            "doc_id": obj.get("doc_id"),
            "ef": obj.get("ef"),
            "filter": QueryFilter.from_dict(obj["filter"]) if obj.get("filter") is not None else None
        })
        return _obj


