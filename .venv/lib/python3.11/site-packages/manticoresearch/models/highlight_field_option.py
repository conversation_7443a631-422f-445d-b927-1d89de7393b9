# coding: utf-8

"""
    Manticore Search Client

    Сlient for Manticore Search. 

    The version of the OpenAPI document: 5.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class HighlightFieldOption(BaseModel):
    """
    Options for controlling the behavior of highlighting on a per-field basis
    """ # noqa: E501
    fragment_size: Optional[StrictInt] = Field(default=None, description="Maximum size of the text fragments in highlighted snippets per field")
    limit: Optional[StrictInt] = Field(default=None, description="Maximum size of snippets per field")
    limit_snippets: Optional[StrictInt] = Field(default=None, description="Maximum number of snippets per field")
    limit_words: Optional[StrictInt] = Field(default=None, description="Maximum number of words per field")
    number_of_fragments: Optional[StrictInt] = Field(default=None, description="Total number of highlighted fragments per field")
    __properties: ClassVar[List[str]] = ["fragment_size", "limit", "limit_snippets", "limit_words", "number_of_fragments"]

    #model_config = ConfigDict(
    #    populate_by_name=True,
    #    validate_assignment=True,
    #    protected_namespaces=(),
    #)


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of HighlightFieldOption from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of HighlightFieldOption from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "fragment_size": obj.get("fragment_size"),
            "limit": obj.get("limit"),
            "limit_snippets": obj.get("limit_snippets"),
            "limit_words": obj.get("limit_words"),
            "number_of_fragments": obj.get("number_of_fragments")
        })
        return _obj


