# coding: utf-8

"""
    Manticore Search Client

    Сlient for Manticore Search. 

    The version of the OpenAPI document: 5.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from manticoresearch.models.bool_filter import BoolFilter
from manticoresearch.models.geo_distance import GeoDistance
from manticoresearch.models.highlight import Highlight
from typing import Optional, Set
from typing_extensions import Self

class SearchQuery(BaseModel):
    """
    Defines a query structure for performing search operations
    """ # noqa: E501
    query_string: Optional[StrictStr] = Field(default=None, description="Filter object defining a query string")
    match: Optional[Dict[str, Any]] = Field(default=None, description="Filter object defining a match keyword passed as a string or in a Match object")
    match_phrase: Optional[Dict[str, Any]] = Field(default=None, description="Filter object defining a match phrase")
    match_all: Optional[Dict[str, Any]] = Field(default=None, description="Filter object to select all documents")
    bool: Optional[BoolFilter] = None
    equals: Optional[Any] = None
    var_in: Optional[Dict[str, Any]] = Field(default=None, description="Filter to match a given set of attribute values.", alias="in")
    range: Optional[Dict[str, Any]] = Field(default=None, description="Filter to match a given range of attribute values passed in Range objects")
    geo_distance: Optional[GeoDistance] = None
    highlight: Optional[Highlight] = None
    __properties: ClassVar[List[str]] = ["query_string", "match", "match_phrase", "match_all", "bool", "equals", "in", "range", "geo_distance", "highlight"]

    #model_config = ConfigDict(
    #    populate_by_name=True,
    #    validate_assignment=True,
    #    protected_namespaces=(),
    #)


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SearchQuery from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of bool
        if self.bool:
            _dict['bool'] = self.bool.to_dict()
        # override the default output from pydantic by calling `to_dict()` of geo_distance
        if self.geo_distance:
            _dict['geo_distance'] = self.geo_distance.to_dict()
        # override the default output from pydantic by calling `to_dict()` of highlight
        if self.highlight:
            _dict['highlight'] = self.highlight.to_dict()
        # set to None if equals (nullable) is None
        # and model_fields_set contains the field
        if self.equals is None and "equals" in self.model_fields_set:
            _dict['equals'] = None

        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SearchQuery from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "query_string": obj.get("query_string"),
            "match": obj.get("match"),
            "match_phrase": obj.get("match_phrase"),
            "match_all": obj.get("match_all"),
            "bool": BoolFilter.from_dict(obj["bool"]) if obj.get("bool") is not None else None,
            "equals": obj.get("equals"),
            "in": obj.get("in"),
            "range": obj.get("range"),
            "geo_distance": GeoDistance.from_dict(obj["geo_distance"]) if obj.get("geo_distance") is not None else None,
            "highlight": Highlight.from_dict(obj["highlight"]) if obj.get("highlight") is not None else None
        })
        return _obj


