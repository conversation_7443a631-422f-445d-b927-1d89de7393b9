# coding: utf-8

"""
    Manticore Search Client

    Сlient for Manticore Search. 

    The version of the OpenAPI document: 5.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class SuccessResponse(BaseModel):
    """
    Response object indicating the success of an operation, such as inserting or updating a document
    """ # noqa: E501
    table: Optional[StrictStr] = Field(default=None, description="Name of the document table")
    id: Optional[StrictInt] = Field(default=None, description="ID of the document affected by the request operation")
    created: Optional[StrictBool] = Field(default=None, description="Indicates whether the document was created as a result of the operation")
    result: Optional[StrictStr] = Field(default=None, description="Result of the operation, typically 'created', 'updated', or 'deleted'")
    found: Optional[StrictBool] = Field(default=None, description="Indicates whether the document was found in the table")
    status: Optional[StrictInt] = Field(default=None, description="HTTP status code representing the result of the operation")
    __properties: ClassVar[List[str]] = ["table", "id", "created", "result", "found", "status"]

    #model_config = ConfigDict(
    #    populate_by_name=True,
    #    validate_assignment=True,
    #    protected_namespaces=(),
    #)


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SuccessResponse from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SuccessResponse from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "table": obj.get("table"),
            "id": obj.get("id"),
            "created": obj.get("created"),
            "result": obj.get("result"),
            "found": obj.get("found"),
            "status": obj.get("status")
        })
        return _obj


