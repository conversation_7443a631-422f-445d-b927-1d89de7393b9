# coding: utf-8

"""
    Manticore Search Client

    Сlient for Manticore Search. 

    The version of the OpenAPI document: 5.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from typing import Optional, Set
from typing_extensions import Self

class HitsHits(BaseModel):
    """
    Search hit representing a matched document
    """ # noqa: E501
    id: Optional[StrictInt] = Field(default=None, description="The ID of the matched document", alias="_id")
    score: Optional[StrictInt] = Field(default=None, description="The score of the matched document", alias="_score")
    source: Optional[Dict[str, Any]] = Field(default=None, description="The source data of the matched document", alias="_source")
    knn_dist: Optional[Union[StrictFloat, StrictInt]] = Field(default=None, description="The knn distance of the matched document returned for knn queries", alias="_knn_dist")
    highlight: Optional[Dict[str, Any]] = Field(default=None, description="The highlighting-related data of the matched document")
    table: Optional[StrictStr] = Field(default=None, description="The table name of the matched document returned for percolate queries")
    type_: Optional[StrictStr] = Field(default=None, description="The type of the matched document returned for percolate queries", alias="_type:")
    fields: Optional[Dict[str, Any]] = Field(default=None, description="The percolate-related fields of the matched document returned for percolate queries")
    __properties: ClassVar[List[str]] = ["_id", "_score", "_source", "_knn_dist", "highlight", "table", "_type:", "fields"]

    #model_config = ConfigDict(
    #    populate_by_name=True,
    #    validate_assignment=True,
    #    protected_namespaces=(),
    #)


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of HitsHits from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of HitsHits from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "_id": obj.get("_id"),
            "_score": obj.get("_score"),
            "_source": obj.get("_source"),
            "_knn_dist": obj.get("_knn_dist"),
            "highlight": obj.get("highlight"),
            "table": obj.get("table"),
            "_type:": obj.get("_type:"),
            "fields": obj.get("fields")
        })
        return _obj


