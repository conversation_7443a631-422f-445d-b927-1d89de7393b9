# coding: utf-8

"""
    Manticore Search Client

    Сlient for Manticore Search. 

    The version of the OpenAPI document: 5.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict
from typing import Any, ClassVar, Dict, List, Optional
from manticoresearch.models.agg_composite import AggComposite
from manticoresearch.models.agg_histogram import AggHistogram
from manticoresearch.models.agg_terms import AggTerms
from typing import Optional, Set
from typing_extensions import Self

class Aggregation(BaseModel):
    """
    Aggregation
    """ # noqa: E501
    terms: Optional[AggTerms] = None
    sort: Optional[List[Any]] = None
    composite: Optional[AggComposite] = None
    histogram: Optional[AggHistogram] = None
    __properties: ClassVar[List[str]] = ["terms", "sort", "composite", "histogram"]

    #model_config = ConfigDict(
    #    populate_by_name=True,
    #    validate_assignment=True,
    #    protected_namespaces=(),
    #)


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of Aggregation from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of terms
        if self.terms:
            _dict['terms'] = self.terms.to_dict()
        # override the default output from pydantic by calling `to_dict()` of composite
        if self.composite:
            _dict['composite'] = self.composite.to_dict()
        # override the default output from pydantic by calling `to_dict()` of histogram
        if self.histogram:
            _dict['histogram'] = self.histogram.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of Aggregation from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "terms": AggTerms.from_dict(obj["terms"]) if obj.get("terms") is not None else None,
            "sort": obj.get("sort"),
            "composite": AggComposite.from_dict(obj["composite"]) if obj.get("composite") is not None else None,
            "histogram": AggHistogram.from_dict(obj["histogram"]) if obj.get("histogram") is not None else None
        })
        return _obj


