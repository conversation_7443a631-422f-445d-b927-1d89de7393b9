# E2 文本分割策略 Bakeoff

对比语义切分与朴素切分对检索质量与上下文贴合度的影响。

步骤：
1. 准备语料：推荐直接使用 `experiments/real_dialogues/*.md`（真实对话/长文），也可自定义 `--corpus_dir`。
2. 运行 `scripts/split_semantic.py` 与 `scripts/split_naive.py` 生成切片。
3. 运行本目录的评测脚本，比较不同切分策略的 Recall@K/nDCG。
4. 人工抽样检查跨标题污染率与 Chunk 统计。

## 接入真实技术栈（最小实施步骤）

目标：将切分策略与 E1 的真实检索评测打通，衡量对 Recall@K/nDCG 的实效影响。

1) 统一数据与切分输出
- 准备 `data/corpus/`，生成两套切分产物：
  - 朴素：`data/slices/naive/*.jsonl`
  - 语义：`data/slices/semantic/*.jsonl`
- 统一字段：`id, doc_id, chunk_id, content, start, end`

2) 嵌入与入库
- 使用与 E1 相同的 embedding 模型，批量编码两套切片；建议在真实对话上评测（`--corpus_dir experiments/real_dialogues`）。
- 将向量和元数据写入对应索引/集合（如 Postgres+pgvector 的 `docs_naive`、`docs_semantic`）。

3) 评测联动
- 可选 A（无外部依赖）：使用自监督评测脚本，直接基于 `test_contents` 生成查询并计算指标：
  - 命令：`python experiments/scripts/eval_splitting_retrieval.py`
  - 输出：`E2_text_splitting/results/metrics_eval.csv`（逐查询）、`metrics_eval_summary.json`（汇总）。
  - 指标：Recall@K、nDCG@K、Top1 覆盖率、跨标题污染率（基于 Markdown 标题）。
- 可选 B（与 E1 联动）：复用 E1 的查询集与评测，分别对 `naive` 与 `semantic` 库检索；
  - 输出两份 `results/metrics_{naive,semantic}.csv`，关键指标对比：Recall@K、nDCG、命中片平均覆盖率等。

4) 汇总与结论
- 将对比结果并入根目录汇总，结合 `semantic_coherence_score` 作为辅佐参考；
- 以“检索效果提升/减速比”呈现收益-成本权衡。推荐阈值：若语义切分在 Recall@5 或 nDCG@5 提升 ≥10%，且速度劣化 ≤30%，优先语义；否则默认朴素，按需在易碎文档启用语义。

## 运行说明（自监督评测）

- 语料：`experiments/test_contents/*.md`
- 命令：`python experiments/scripts/eval_splitting_retrieval.py`
- 产物：
  - `experiments/E2_text_splitting/results/metrics_eval.csv`
  - `experiments/E2_text_splitting/results/metrics_eval_summary.json`
  - 控制台摘要包含：chunks/avg_len/bleed/R@5/nDCG@5/top1_coverage

备注：默认采用字符 n-gram + 余弦相似度的轻量检索模型；若需要接近真实效果，可切换向量模式并指定真实语料：
`python experiments/scripts/eval_splitting_retrieval.py --mode vector --corpus_dir experiments/real_dialogues`
