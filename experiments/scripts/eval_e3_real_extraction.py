#!/usr/bin/env python3
"""
E3 结构化抽取真实测试（使用 OpenAI API vs 规则化方法）

目标：使用真实的 LLM API 进行结构化抽取，与规则化方法对比：
- 准确性：主题分类、实体识别、关键点抽取
- 性能：处理时间、成功率、成本
- 实用性：在真实对话场景中的表现
"""

import os
import re
import csv
import json
import time
import argparse
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

# 尝试导入依赖
try:
    import openai
    from dotenv import load_dotenv
    OPENAI_AVAILABLE = True
    load_dotenv()
except ImportError:
    OPENAI_AVAILABLE = False

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 默认使用真实对话作为测试语料，可通过 --corpus_dir 覆盖
DEFAULT_CORPUS_DIR = os.path.join(ROOT, 'real_dialogues')
RESULTS_DIR = os.path.join(ROOT, 'E3_extraction', 'results')
os.makedirs(RESULTS_DIR, exist_ok=True)

@dataclass
class ExtractionResult:
    """抽取结果"""
    method: str
    text_id: str
    processing_time_ms: float
    success: bool
    confidence_score: float
    topic: str
    key_points: List[str]
    entities: List[Dict[str, Any]]
    summary: str
    error_message: Optional[str] = None

class OpenAIExtractor:
    """基于 OpenAI API 的结构化抽取器"""
    
    def __init__(self, model: str = "gpt-4o-mini"):
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI 不可用，请安装: pip install openai python-dotenv")
        
        self.client = openai.OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_BASE_URL')
        )
        self.model = model
        
    def extract(self, text: str, text_id: str) -> ExtractionResult:
        """使用 OpenAI API 进行结构化抽取"""
        start_time = time.time()
        
        try:
            prompt = self._build_extraction_prompt(text)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的文本分析助手，擅长从文本中提取结构化信息。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            result_json = json.loads(response.choices[0].message.content)
            processing_time = (time.time() - start_time) * 1000
            
            return ExtractionResult(
                method='openai_llm',
                text_id=text_id,
                processing_time_ms=processing_time,
                success=True,
                confidence_score=result_json.get('confidence', 0.8),
                topic=result_json.get('topic', '未知'),
                key_points=result_json.get('key_points', []),
                entities=result_json.get('entities', []),
                summary=result_json.get('summary', '')
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            return ExtractionResult(
                method='openai_llm',
                text_id=text_id,
                processing_time_ms=processing_time,
                success=False,
                confidence_score=0.0,
                topic='',
                key_points=[],
                entities=[],
                summary='',
                error_message=str(e)
            )
    
    def _build_extraction_prompt(self, text: str) -> str:
        """构建抽取提示词"""
        return f"""请从以下文本中提取结构化信息，以JSON格式返回：

文本内容：
{text}

请提取以下信息：
1. topic: 文本的主要主题（从以下选择：历史、政治、经济、文化、军事、哲学、其他）
2. key_points: 3-5个关键要点（每个要点20-50字）
3. entities: 重要实体列表，每个实体包含 text（实体文本）、type（类型：人物、地点、事件、概念）、confidence（置信度0-1）
4. summary: 文本摘要（100-200字）
5. confidence: 整体抽取置信度（0-1）

返回格式：
{{
    "topic": "主题",
    "key_points": ["要点1", "要点2", "要点3"],
    "entities": [
        {{"text": "实体名", "type": "类型", "confidence": 0.9}}
    ],
    "summary": "摘要内容",
    "confidence": 0.85
}}"""

class RuleBasedExtractor:
    """基于规则的抽取器（改进版）"""
    
    def extract(self, text: str, text_id: str) -> ExtractionResult:
        """使用规则进行结构化抽取"""
        start_time = time.time()
        
        try:
            topic = self._extract_topic(text)
            key_points = self._extract_key_points(text)
            entities = self._extract_entities(text)
            summary = self._generate_summary(text)
            
            processing_time = (time.time() - start_time) * 1000
            
            return ExtractionResult(
                method='rule_based',
                text_id=text_id,
                processing_time_ms=processing_time,
                success=True,
                confidence_score=0.75,
                topic=topic,
                key_points=key_points,
                entities=entities,
                summary=summary
            )
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            return ExtractionResult(
                method='rule_based',
                text_id=text_id,
                processing_time_ms=processing_time,
                success=False,
                confidence_score=0.0,
                topic='',
                key_points=[],
                entities=[],
                summary='',
                error_message=str(e)
            )
    
    def _extract_topic(self, text: str) -> str:
        """基于关键词的主题分类"""
        topic_keywords = {
            '历史': ['历史', '朝代', '皇帝', '王朝', '古代', '近代', '现代', '年代', '世纪'],
            '政治': ['政治', '政府', '国家', '政策', '制度', '权力', '统治', '治理'],
            '经济': ['经济', '贸易', '商业', '货币', '财政', '税收', '市场', '产业'],
            '文化': ['文化', '思想', '哲学', '宗教', '艺术', '文学', '教育', '传统'],
            '军事': ['军事', '战争', '军队', '战略', '战术', '武器', '征战', '防御'],
            '哲学': ['哲学', '思辨', '理论', '观念', '思维', '逻辑', '认识', '价值']
        }
        
        topic_scores = {}
        for topic, keywords in topic_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                topic_scores[topic] = score
        
        if topic_scores:
            return max(topic_scores, key=topic_scores.get)
        return '其他'
    
    def _extract_key_points(self, text: str) -> List[str]:
        """提取关键要点"""
        # 按句号分割，选择长度适中的句子
        sentences = [s.strip() for s in text.split('。') if s.strip()]
        key_points = []
        
        for sentence in sentences:
            if 20 <= len(sentence) <= 80:  # 长度适中的句子
                key_points.append(sentence)
        
        return key_points[:5]  # 最多5个要点
    
    def _extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """提取实体"""
        entities = []
        
        # 人物实体（中文姓名模式）
        person_pattern = r'[王李张刘陈杨黄赵吴周徐孙马朱胡郭何高林罗郑梁谢宋唐许韩冯邓曹彭曾肖田董袁潘于蒋蔡余杜叶程苏魏吕丁任沈姚卢姜崔钟谭陆汪范金石廖贾夏韦付方白邹孟熊秦邱江尹薛闫段雷侯龙史陶黎贺顾毛郝龚邵万钱严覃武戴莫孔向汤][\u4e00-\u9fa5]{1,3}'
        persons = re.findall(person_pattern, text)
        for person in set(persons):  # 去重
            if len(person) >= 2:  # 至少两个字
                entities.append({
                    'text': person,
                    'type': '人物',
                    'confidence': 0.8
                })
        
        # 地点实体
        place_keywords = ['中国', '北京', '上海', '广州', '深圳', '天津', '重庆', '南京', '杭州', '成都', '西安', '武汉', '长安', '洛阳', '开封', '临安']
        for place in place_keywords:
            if place in text:
                entities.append({
                    'text': place,
                    'type': '地点',
                    'confidence': 0.9
                })
        
        return entities[:10]  # 最多10个实体
    
    def _generate_summary(self, text: str) -> str:
        """生成摘要"""
        sentences = [s.strip() for s in text.split('。') if s.strip()]
        if len(sentences) >= 3:
            return '。'.join(sentences[:3]) + '。'
        elif sentences:
            return '。'.join(sentences) + '。'
        else:
            return text[:100] + '...' if len(text) > 100 else text

def load_test_texts(corpus_dir: str, max_docs: int = 10) -> List[Dict[str, str]]:
    """从指定目录加载测试文本（默认 real_dialogues）"""
    import glob
    
    texts = []
    files = sorted(glob.glob(os.path.join(corpus_dir, '*.md')))[:max_docs]
    
    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 取前1000字符作为测试文本
            text_content = content[:1000] if len(content) > 1000 else content
            
            texts.append({
                'id': os.path.splitext(os.path.basename(file_path))[0],
                'content': text_content
            })
        except Exception as e:
            print(f"读取文件 {file_path} 失败: {e}")
            continue
    
    return texts

def main():
    parser = argparse.ArgumentParser(description='E3 结构化抽取真实测试')
    parser.add_argument('--max-docs', type=int, default=10, help='最大测试文档数')
    parser.add_argument('--corpus_dir', default=DEFAULT_CORPUS_DIR, help='语料目录（默认 experiments/real_dialogues）')
    parser.add_argument('--openai-model', default='gpt-4o-mini', help='OpenAI 模型名称')
    args = parser.parse_args()
    
    print("开始 E3 结构化抽取真实测试...")
    
    # 加载测试文本
    test_texts = load_test_texts(args.corpus_dir, args.max_docs)
    if not test_texts:
        print(f"未找到测试文本，请检查 {args.corpus_dir}")
        return
    
    print(f"加载了 {len(test_texts)} 个测试文本")
    
    # 初始化抽取器
    extractors = []
    
    # 规则化抽取器
    rule_extractor = RuleBasedExtractor()
    extractors.append(('rule_based', rule_extractor))
    
    # OpenAI 抽取器
    if OPENAI_AVAILABLE:
        try:
            openai_extractor = OpenAIExtractor(args.openai_model)
            extractors.append(('openai_llm', openai_extractor))
            print(f"使用 OpenAI 模型: {args.openai_model}")
        except Exception as e:
            print(f"OpenAI 初始化失败: {e}")
    else:
        print("OpenAI 不可用，仅运行规则化测试")
    
    # 运行测试
    all_results = []
    
    for text_data in test_texts:
        print(f"处理文本: {text_data['id']}")
        
        for extractor_name, extractor in extractors:
            result = extractor.extract(text_data['content'], text_data['id'])
            all_results.append(result)
            
            if result.success:
                print(f"  {extractor_name}: 成功 - 主题:{result.topic}, 要点:{len(result.key_points)}个, 实体:{len(result.entities)}个")
            else:
                print(f"  {extractor_name}: 失败 - {result.error_message}")
    
    # 保存结果
    save_results(all_results)
    
    # 打印摘要
    print_summary(all_results)

def save_results(results: List[ExtractionResult]):
    """保存结果到文件"""
    # CSV 结果
    csv_path = os.path.join(RESULTS_DIR, 'experiment_results.csv')
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([
            'method', 'text_id', 'processing_time_ms', 'success', 'confidence_score',
            'topic', 'key_points_count', 'entities_count', 'summary_length', 'error_message'
        ])
        
        for result in results:
            writer.writerow([
                result.method, result.text_id, result.processing_time_ms, result.success,
                result.confidence_score, result.topic, len(result.key_points),
                len(result.entities), len(result.summary), result.error_message or ''
            ])
    
    # JSON 详细结果
    json_path = os.path.join(RESULTS_DIR, 'detailed_results.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        results_data = [asdict(result) for result in results]
        json.dump(results_data, f, indent=2, ensure_ascii=False)
    
    print(f"结果已保存到: {csv_path}")

def print_summary(results: List[ExtractionResult]):
    """打印测试摘要"""
    print("\n" + "="*60)
    print("E3 结构化抽取真实测试摘要")
    print("="*60)
    
    # 按方法分组
    methods = {}
    for result in results:
        if result.method not in methods:
            methods[result.method] = []
        methods[result.method].append(result)
    
    for method, method_results in methods.items():
        successful = [r for r in method_results if r.success]
        
        print(f"\n{method.upper()} 性能:")
        print(f"  成功率: {len(successful)}/{len(method_results)} ({len(successful)/len(method_results):.1%})")
        
        if successful:
            avg_time = sum(r.processing_time_ms for r in successful) / len(successful)
            avg_confidence = sum(r.confidence_score for r in successful) / len(successful)
            avg_key_points = sum(len(r.key_points) for r in successful) / len(successful)
            avg_entities = sum(len(r.entities) for r in successful) / len(successful)
            
            print(f"  平均处理时间: {avg_time:.1f}ms")
            print(f"  平均置信度: {avg_confidence:.3f}")
            print(f"  平均关键点数: {avg_key_points:.1f}")
            print(f"  平均实体数: {avg_entities:.1f}")

if __name__ == "__main__":
    main()
