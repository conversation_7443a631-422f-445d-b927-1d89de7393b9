#!/usr/bin/env python3
"""
E5 事件一致性真实测试（Outbox vs Streams）

目标：在模拟的分布式场景中测试两种事件一致性方案：
- Outbox Pattern：事务性发件箱模式
- Event Streams：事件流模式
- 对比指标：成功率、顺序保证、延迟、吞吐量
"""

import os
import time
import json
import csv
import random
import threading
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import queue
import sqlite3

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
RESULTS_DIR = os.path.join(ROOT, 'E5_events_consistency', 'results')
os.makedirs(RESULTS_DIR, exist_ok=True)

@dataclass
class ConsistencyResult:
    """一致性测试结果"""
    approach: str
    test_scenario: str
    success_rate: float
    ordering_rate: float  # 事件顺序正确率
    avg_latency_ms: float
    p99_latency_ms: float
    throughput_events_per_sec: float
    error_count: int

class EventType(Enum):
    """事件类型"""
    USER_MESSAGE = "user_message"
    AI_RESPONSE = "ai_response"
    SUMMARY_UPDATE = "summary_update"
    SESSION_END = "session_end"

@dataclass
class Event:
    """事件定义"""
    id: str
    type: EventType
    session_id: str
    sequence: int
    payload: Dict[str, Any]
    timestamp: float

# ==================== Outbox Pattern 实现 ====================

class OutboxEventProcessor:
    """基于 Outbox Pattern 的事件处理器"""
    
    def __init__(self):
        self.db_path = os.path.join(RESULTS_DIR, 'outbox.db')
        self._init_db()
        self.processed_events = []
        self.processing_times = []
    
    def _init_db(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
            CREATE TABLE IF NOT EXISTS outbox (
                id TEXT PRIMARY KEY,
                event_type TEXT,
                session_id TEXT,
                sequence INTEGER,
                payload TEXT,
                timestamp REAL,
                processed BOOLEAN DEFAULT FALSE
            )
        ''')
        conn.commit()
        conn.close()
    
    def process_events(self, events: List[Event], scenario: str) -> ConsistencyResult:
        """处理事件序列"""
        start_time = time.time()
        success_count = 0
        error_count = 0
        latencies = []
        
        # 清理之前的数据
        self._clear_outbox()
        self.processed_events = []
        
        for event in events:
            event_start = time.time()
            
            try:
                # 1. 事务性写入 outbox
                self._write_to_outbox(event)
                
                # 2. 模拟业务处理
                self._process_business_logic(event)
                
                # 3. 标记为已处理
                self._mark_processed(event.id)
                
                success_count += 1
                self.processed_events.append(event)
                
                event_latency = (time.time() - event_start) * 1000
                latencies.append(event_latency)
                
            except Exception as e:
                error_count += 1
                print(f"Outbox 处理事件 {event.id} 失败: {e}")
        
        total_time = time.time() - start_time
        
        # 计算指标
        success_rate = success_count / len(events) if events else 0
        ordering_rate = self._check_ordering_correctness(events, self.processed_events)
        avg_latency = sum(latencies) / len(latencies) if latencies else 0
        p99_latency = sorted(latencies)[int(len(latencies) * 0.99)] if latencies else 0
        throughput = len(events) / total_time if total_time > 0 else 0
        
        return ConsistencyResult(
            approach='outbox',
            test_scenario=scenario,
            success_rate=success_rate,
            ordering_rate=ordering_rate,
            avg_latency_ms=avg_latency,
            p99_latency_ms=p99_latency,
            throughput_events_per_sec=throughput,
            error_count=error_count
        )
    
    def _write_to_outbox(self, event: Event):
        """写入 outbox 表"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('''
            INSERT INTO outbox (id, event_type, session_id, sequence, payload, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (event.id, event.type.value, event.session_id, event.sequence, 
              json.dumps(event.payload), event.timestamp))
        conn.commit()
        conn.close()
        
        # 模拟写入延迟
        time.sleep(0.001)
    
    def _process_business_logic(self, event: Event):
        """模拟业务逻辑处理"""
        # 模拟不同类型事件的处理时间
        if event.type == EventType.USER_MESSAGE:
            time.sleep(0.005)
        elif event.type == EventType.AI_RESPONSE:
            time.sleep(0.01)
        elif event.type == EventType.SUMMARY_UPDATE:
            time.sleep(0.003)
        else:
            time.sleep(0.002)
    
    def _mark_processed(self, event_id: str):
        """标记事件为已处理"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('UPDATE outbox SET processed = TRUE WHERE id = ?', (event_id,))
        conn.commit()
        conn.close()
    
    def _clear_outbox(self):
        """清理 outbox"""
        conn = sqlite3.connect(self.db_path)
        conn.execute('DELETE FROM outbox')
        conn.commit()
        conn.close()
    
    def _check_ordering_correctness(self, original: List[Event], processed: List[Event]) -> float:
        """检查事件顺序正确性"""
        if not original or not processed:
            return 0.0
        
        # 按 session_id 分组检查顺序
        sessions = {}
        for event in original:
            if event.session_id not in sessions:
                sessions[event.session_id] = []
            sessions[event.session_id].append(event.sequence)
        
        processed_sessions = {}
        for event in processed:
            if event.session_id not in processed_sessions:
                processed_sessions[event.session_id] = []
            processed_sessions[event.session_id].append(event.sequence)
        
        correct_sessions = 0
        total_sessions = len(sessions)
        
        for session_id, original_seq in sessions.items():
            processed_seq = processed_sessions.get(session_id, [])
            if original_seq == processed_seq:
                correct_sessions += 1
        
        return correct_sessions / total_sessions if total_sessions > 0 else 0.0

# ==================== Event Streams 实现 ====================

class StreamEventProcessor:
    """基于事件流的处理器"""
    
    def __init__(self):
        self.event_queue = queue.Queue()
        self.processed_events = []
        self.processing_times = []
        self.is_processing = False
    
    def process_events(self, events: List[Event], scenario: str) -> ConsistencyResult:
        """处理事件序列"""
        start_time = time.time()
        success_count = 0
        error_count = 0
        latencies = []
        
        # 清理之前的数据
        self.processed_events = []
        
        # 启动消费者线程
        consumer_thread = threading.Thread(target=self._event_consumer)
        consumer_thread.daemon = True
        self.is_processing = True
        consumer_thread.start()
        
        # 生产事件
        for event in events:
            event_start = time.time()
            
            try:
                # 发送到事件流
                self.event_queue.put((event, event_start))
                success_count += 1
                
            except Exception as e:
                error_count += 1
                print(f"Streams 发送事件 {event.id} 失败: {e}")
        
        # 等待所有事件处理完成
        while len(self.processed_events) < success_count:
            time.sleep(0.001)
        
        self.is_processing = False
        total_time = time.time() - start_time
        
        # 计算延迟
        for _, latency in self.processing_times:
            latencies.append(latency)
        
        # 计算指标
        success_rate = success_count / len(events) if events else 0
        ordering_rate = self._check_ordering_correctness(events, self.processed_events)
        avg_latency = sum(latencies) / len(latencies) if latencies else 0
        p99_latency = sorted(latencies)[int(len(latencies) * 0.99)] if latencies else 0
        throughput = len(events) / total_time if total_time > 0 else 0
        
        return ConsistencyResult(
            approach='streams',
            test_scenario=scenario,
            success_rate=success_rate,
            ordering_rate=ordering_rate,
            avg_latency_ms=avg_latency,
            p99_latency_ms=p99_latency,
            throughput_events_per_sec=throughput,
            error_count=error_count
        )
    
    def _event_consumer(self):
        """事件消费者"""
        while self.is_processing or not self.event_queue.empty():
            try:
                event, start_time = self.event_queue.get(timeout=0.1)
                
                # 模拟事件处理
                self._process_event(event)
                
                # 记录处理时间
                latency = (time.time() - start_time) * 1000
                self.processing_times.append((event.id, latency))
                self.processed_events.append(event)
                
                self.event_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"消费事件失败: {e}")
    
    def _process_event(self, event: Event):
        """处理单个事件"""
        # 模拟不同类型事件的处理时间
        if event.type == EventType.USER_MESSAGE:
            time.sleep(0.005)
        elif event.type == EventType.AI_RESPONSE:
            time.sleep(0.01)
        elif event.type == EventType.SUMMARY_UPDATE:
            time.sleep(0.003)
        else:
            time.sleep(0.002)
    
    def _check_ordering_correctness(self, original: List[Event], processed: List[Event]) -> float:
        """检查事件顺序正确性"""
        if not original or not processed:
            return 0.0
        
        # 按 session_id 分组检查顺序
        sessions = {}
        for event in original:
            if event.session_id not in sessions:
                sessions[event.session_id] = []
            sessions[event.session_id].append(event.sequence)
        
        processed_sessions = {}
        for event in processed:
            if event.session_id not in processed_sessions:
                processed_sessions[event.session_id] = []
            processed_sessions[event.session_id].append(event.sequence)
        
        correct_sessions = 0
        total_sessions = len(sessions)
        
        for session_id, original_seq in sessions.items():
            processed_seq = processed_sessions.get(session_id, [])
            if original_seq == processed_seq:
                correct_sessions += 1
        
        return correct_sessions / total_sessions if total_sessions > 0 else 0.0

def generate_test_events(scenario: str) -> List[Event]:
    """生成测试事件"""
    events = []
    
    if scenario == "single_session":
        # 单会话场景
        session_id = "session_1"
        for i in range(10):
            events.append(Event(
                id=f"event_{i}",
                type=EventType.USER_MESSAGE if i % 2 == 0 else EventType.AI_RESPONSE,
                session_id=session_id,
                sequence=i,
                payload={"content": f"Message {i}"},
                timestamp=time.time()
            ))
    
    elif scenario == "multi_session":
        # 多会话场景
        for session in range(3):
            session_id = f"session_{session}"
            for i in range(5):
                events.append(Event(
                    id=f"event_{session}_{i}",
                    type=random.choice(list(EventType)),
                    session_id=session_id,
                    sequence=i,
                    payload={"content": f"Session {session} Message {i}"},
                    timestamp=time.time()
                ))
    
    elif scenario == "high_throughput":
        # 高吞吐量场景
        for i in range(50):
            events.append(Event(
                id=f"event_{i}",
                type=random.choice(list(EventType)),
                session_id=f"session_{i % 5}",
                sequence=i // 5,
                payload={"content": f"High throughput message {i}"},
                timestamp=time.time()
            ))
    
    return events

def main():
    print("开始 E5 事件一致性真实测试...")
    
    # 测试场景
    scenarios = ["single_session", "multi_session", "high_throughput"]
    
    # 初始化处理器
    outbox_processor = OutboxEventProcessor()
    stream_processor = StreamEventProcessor()
    
    all_results = []
    
    for scenario in scenarios:
        print(f"测试场景: {scenario}")
        
        # 生成测试事件
        events = generate_test_events(scenario)
        print(f"  生成了 {len(events)} 个事件")
        
        # Outbox 测试
        outbox_result = outbox_processor.process_events(events, scenario)
        all_results.append(outbox_result)
        print(f"  Outbox: 成功率 {outbox_result.success_rate:.1%}, 顺序率 {outbox_result.ordering_rate:.1%}")
        
        # Streams 测试
        stream_result = stream_processor.process_events(events, scenario)
        all_results.append(stream_result)
        print(f"  Streams: 成功率 {stream_result.success_rate:.1%}, 顺序率 {stream_result.ordering_rate:.1%}")
    
    # 保存结果
    save_results(all_results)
    
    # 打印摘要
    print_summary(all_results)

def save_results(results: List[ConsistencyResult]):
    """保存结果到文件"""
    csv_path = os.path.join(RESULTS_DIR, 'metrics.csv')
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([
            'approach', 'test_scenario', 'success_rate', 'ordering_rate',
            'avg_latency_ms', 'p99_latency_ms', 'throughput_events_per_sec', 'error_count'
        ])
        
        for result in results:
            writer.writerow([
                result.approach, result.test_scenario, result.success_rate,
                result.ordering_rate, result.avg_latency_ms, result.p99_latency_ms,
                result.throughput_events_per_sec, result.error_count
            ])
    
    print(f"结果已保存到: {csv_path}")

def print_summary(results: List[ConsistencyResult]):
    """打印测试摘要"""
    print("\n" + "="*60)
    print("E5 事件一致性真实测试摘要")
    print("="*60)
    
    # 按方法分组
    approaches = {}
    for result in results:
        if result.approach not in approaches:
            approaches[result.approach] = []
        approaches[result.approach].append(result)
    
    for approach, approach_results in approaches.items():
        print(f"\n{approach.upper()} 性能:")
        
        avg_success = sum(r.success_rate for r in approach_results) / len(approach_results)
        avg_ordering = sum(r.ordering_rate for r in approach_results) / len(approach_results)
        avg_latency = sum(r.avg_latency_ms for r in approach_results) / len(approach_results)
        avg_p99 = sum(r.p99_latency_ms for r in approach_results) / len(approach_results)
        
        print(f"  平均成功率: {avg_success:.1%}")
        print(f"  平均顺序率: {avg_ordering:.1%}")
        print(f"  平均延迟: {avg_latency:.1f}ms")
        print(f"  P99 延迟: {avg_p99:.1f}ms")

if __name__ == "__main__":
    main()
