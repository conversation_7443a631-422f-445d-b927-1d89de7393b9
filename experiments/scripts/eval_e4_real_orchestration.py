#!/usr/bin/env python3
"""
E4 编排对比真实测试（本地状态机 vs LangGraph）

目标：在真实的对话处理工作流中对比两种编排方式：
- 本地状态机：简单的 Pydantic 状态管理
- LangGraph：图状态编排框架
- 对比指标：处理时间、内存占用、成功率、可维护性
"""

import os
import time
import json
import csv
import psutil
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

# 尝试导入 LangGraph
try:
    from langgraph.graph import StateGraph, END
    from langgraph.graph.message import add_messages
    from typing_extensions import TypedDict
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 可选加载真实对话以构造更贴近实际的输入
DEFAULT_CORPUS_DIR = os.path.join(ROOT, 'real_dialogues')
RESULTS_DIR = os.path.join(ROOT, 'E4_orchestration', 'results')
os.makedirs(RESULTS_DIR, exist_ok=True)

@dataclass
class OrchestrationResult:
    """编排测试结果"""
    orchestrator: str
    test_case: str
    total_time_ms: float
    memory_usage_mb: float
    success: bool
    steps_completed: int
    error_message: Optional[str] = None

class ConversationState(Enum):
    """对话状态"""
    INIT = "init"
    INTENT_ANALYSIS = "intent_analysis"
    RETRIEVAL = "retrieval"
    GENERATION = "generation"
    FORMATTING = "formatting"
    COMPLETED = "completed"
    ERROR = "error"

# ==================== 本地状态机实现 ====================

class LocalStateMachine:
    """本地状态机编排器"""
    
    def __init__(self):
        self.state = ConversationState.INIT
        self.context = {}
        self.steps_completed = 0
    
    def process_conversation(self, user_input: str, test_case: str) -> OrchestrationResult:
        """处理对话的完整流程"""
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            self.context = {"user_input": user_input, "test_case": test_case}
            self.steps_completed = 0
            
            # 状态机流程
            self._transition_to(ConversationState.INTENT_ANALYSIS)
            self._analyze_intent()
            
            self._transition_to(ConversationState.RETRIEVAL)
            self._retrieve_context()
            
            self._transition_to(ConversationState.GENERATION)
            self._generate_response()
            
            self._transition_to(ConversationState.FORMATTING)
            self._format_output()
            
            self._transition_to(ConversationState.COMPLETED)
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            return OrchestrationResult(
                orchestrator='local_state_machine',
                test_case=test_case,
                total_time_ms=(end_time - start_time) * 1000,
                memory_usage_mb=max(end_memory - start_memory, 0),
                success=True,
                steps_completed=self.steps_completed
            )
            
        except Exception as e:
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            return OrchestrationResult(
                orchestrator='local_state_machine',
                test_case=test_case,
                total_time_ms=(end_time - start_time) * 1000,
                memory_usage_mb=max(end_memory - start_memory, 0),
                success=False,
                steps_completed=self.steps_completed,
                error_message=str(e)
            )
    
    def _transition_to(self, new_state: ConversationState):
        """状态转换"""
        self.state = new_state
        self.steps_completed += 1
    
    def _analyze_intent(self):
        """意图分析"""
        time.sleep(0.05)  # 模拟处理时间
        user_input = self.context["user_input"]
        
        if "问题" in user_input or "什么" in user_input:
            intent = "question"
        elif "总结" in user_input or "摘要" in user_input:
            intent = "summarize"
        else:
            intent = "general"
        
        self.context["intent"] = intent
    
    def _retrieve_context(self):
        """检索相关上下文"""
        time.sleep(0.1)  # 模拟检索时间
        intent = self.context["intent"]
        
        # 模拟检索结果
        if intent == "question":
            context = "相关问题的背景信息和答案要点"
        elif intent == "summarize":
            context = "需要总结的文档内容和关键信息"
        else:
            context = "通用对话上下文信息"
        
        self.context["retrieved_context"] = context
    
    def _generate_response(self):
        """生成回复"""
        time.sleep(0.15)  # 模拟生成时间
        intent = self.context["intent"]
        context = self.context["retrieved_context"]
        
        # 模拟回复生成
        if intent == "question":
            response = f"基于检索到的信息：{context}，这里是详细回答..."
        elif intent == "summarize":
            response = f"根据文档内容：{context}，总结如下..."
        else:
            response = f"理解您的需求，基于上下文：{context}，我的回复是..."
        
        self.context["response"] = response
    
    def _format_output(self):
        """格式化输出"""
        time.sleep(0.02)  # 模拟格式化时间
        response = self.context["response"]
        
        # 简单的格式化
        formatted = {
            "content": response,
            "metadata": {
                "intent": self.context["intent"],
                "processing_steps": self.steps_completed,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        self.context["formatted_output"] = formatted
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024

# ==================== LangGraph 实现 ====================

if LANGGRAPH_AVAILABLE:
    class GraphState(TypedDict):
        """LangGraph 状态定义"""
        user_input: str
        test_case: str
        intent: str
        retrieved_context: str
        response: str
        formatted_output: dict
        steps_completed: int

    class LangGraphOrchestrator:
        """LangGraph 编排器"""
        
        def __init__(self):
            self.graph = self._build_graph()
        
        def _build_graph(self):
            """构建 LangGraph"""
            workflow = StateGraph(GraphState)
            
            # 添加节点
            workflow.add_node("analyze_intent", self._analyze_intent)
            workflow.add_node("retrieve_context", self._retrieve_context)
            workflow.add_node("generate_response", self._generate_response)
            workflow.add_node("format_output", self._format_output)
            
            # 定义边
            workflow.set_entry_point("analyze_intent")
            workflow.add_edge("analyze_intent", "retrieve_context")
            workflow.add_edge("retrieve_context", "generate_response")
            workflow.add_edge("generate_response", "format_output")
            workflow.add_edge("format_output", END)
            
            return workflow.compile()
        
        def process_conversation(self, user_input: str, test_case: str) -> OrchestrationResult:
            """处理对话的完整流程"""
            start_time = time.time()
            start_memory = self._get_memory_usage()
            
            try:
                initial_state = GraphState(
                    user_input=user_input,
                    test_case=test_case,
                    intent="",
                    retrieved_context="",
                    response="",
                    formatted_output={},
                    steps_completed=0
                )
                
                # 运行图
                final_state = self.graph.invoke(initial_state)
                
                end_time = time.time()
                end_memory = self._get_memory_usage()
                
                return OrchestrationResult(
                    orchestrator='langgraph_lite',
                    test_case=test_case,
                    total_time_ms=(end_time - start_time) * 1000,
                    memory_usage_mb=max(end_memory - start_memory, 0),
                    success=True,
                    steps_completed=final_state["steps_completed"]
                )
                
            except Exception as e:
                end_time = time.time()
                end_memory = self._get_memory_usage()
                
                return OrchestrationResult(
                    orchestrator='langgraph_lite',
                    test_case=test_case,
                    total_time_ms=(end_time - start_time) * 1000,
                    memory_usage_mb=max(end_memory - start_memory, 0),
                    success=False,
                    steps_completed=0,
                    error_message=str(e)
                )
        
        def _analyze_intent(self, state: GraphState) -> GraphState:
            """意图分析节点"""
            time.sleep(0.05)
            user_input = state["user_input"]
            
            if "问题" in user_input or "什么" in user_input:
                intent = "question"
            elif "总结" in user_input or "摘要" in user_input:
                intent = "summarize"
            else:
                intent = "general"
            
            state["intent"] = intent
            state["steps_completed"] += 1
            return state
        
        def _retrieve_context(self, state: GraphState) -> GraphState:
            """检索上下文节点"""
            time.sleep(0.1)
            intent = state["intent"]
            
            if intent == "question":
                context = "相关问题的背景信息和答案要点"
            elif intent == "summarize":
                context = "需要总结的文档内容和关键信息"
            else:
                context = "通用对话上下文信息"
            
            state["retrieved_context"] = context
            state["steps_completed"] += 1
            return state
        
        def _generate_response(self, state: GraphState) -> GraphState:
            """生成回复节点"""
            time.sleep(0.15)
            intent = state["intent"]
            context = state["retrieved_context"]
            
            if intent == "question":
                response = f"基于检索到的信息：{context}，这里是详细回答..."
            elif intent == "summarize":
                response = f"根据文档内容：{context}，总结如下..."
            else:
                response = f"理解您的需求，基于上下文：{context}，我的回复是..."
            
            state["response"] = response
            state["steps_completed"] += 1
            return state
        
        def _format_output(self, state: GraphState) -> GraphState:
            """格式化输出节点"""
            time.sleep(0.02)
            response = state["response"]
            
            formatted = {
                "content": response,
                "metadata": {
                    "intent": state["intent"],
                    "processing_steps": state["steps_completed"] + 1,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            state["formatted_output"] = formatted
            state["steps_completed"] += 1
            return state
        
        def _get_memory_usage(self) -> float:
            """获取当前内存使用量（MB）"""
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024

def generate_test_cases(corpus_dir: Optional[str] = None, max_cases: int = 8) -> List[Dict[str, str]]:
    """生成测试用例：优先从真实对话中抽取用户发言作为输入，其次使用内置样例。"""
    cases: List[Dict[str, str]] = []
    if corpus_dir:
        import glob
        paths = sorted(glob.glob(os.path.join(corpus_dir, '*.md')))
        for p in paths:
            try:
                with open(p, 'r', encoding='utf-8') as f:
                    text = f.read()
                # 简单抽取用户段落
                for line in text.splitlines():
                    if line.strip().startswith('## 🧑‍💻 User'):
                        # 下一行或后续非空行为用户输入
                        # 取接下来若干行非标记文本
                        pass
                # 退而求其次：取文档首段标题后的一段文字
                first = next((l for l in text.splitlines() if l.strip() and not l.strip().startswith('#')), None)
                if first:
                    cases.append({"id": os.path.splitext(os.path.basename(p))[0], "input": first[:120]})
                if len(cases) >= max_cases:
                    break
            except Exception:
                continue
    if not cases:
        cases = [
            {"id": "question_1", "input": "什么是机器学习？"},
            {"id": "question_2", "input": "请问人工智能的发展历史是怎样的？"},
            {"id": "summarize_1", "input": "请总结这篇文档的主要内容"},
            {"id": "summarize_2", "input": "帮我做一个摘要"},
            {"id": "general_1", "input": "你好，我想了解一些信息"},
            {"id": "general_2", "input": "能帮我分析一下这个问题吗？"},
        ]
    return cases[:max_cases]

def main():
    print("开始 E4 编排对比真实测试...")
    import argparse
    parser = argparse.ArgumentParser(description='E4 编排对比真实测试')
    parser.add_argument('--corpus_dir', default=DEFAULT_CORPUS_DIR, help='语料目录（默认 experiments/real_dialogues）')
    parser.add_argument('--max_cases', type=int, default=8, help='最大用例数')
    args = parser.parse_args()
    # 生成测试用例（优先真实对话）
    test_cases = generate_test_cases(args.corpus_dir, args.max_cases)
    print(f"生成了 {len(test_cases)} 个测试用例")
    
    # 初始化编排器
    orchestrators = []
    
    # 本地状态机
    local_fsm = LocalStateMachine()
    orchestrators.append(('local_state_machine', local_fsm))
    
    # LangGraph
    if LANGGRAPH_AVAILABLE:
        try:
            langgraph_orch = LangGraphOrchestrator()
            orchestrators.append(('langgraph_lite', langgraph_orch))
            print("LangGraph 编排器已初始化")
        except Exception as e:
            print(f"LangGraph 初始化失败: {e}")
    else:
        print("LangGraph 不可用，仅测试本地状态机")
    
    # 运行测试
    all_results = []
    
    for test_case in test_cases:
        print(f"处理测试用例: {test_case['id']}")
        
        for orch_name, orchestrator in orchestrators:
            result = orchestrator.process_conversation(test_case['input'], test_case['id'])
            all_results.append(result)
            
            if result.success:
                print(f"  {orch_name}: 成功 - {result.total_time_ms:.1f}ms, {result.memory_usage_mb:.1f}MB")
            else:
                print(f"  {orch_name}: 失败 - {result.error_message}")
    
    # 保存结果
    save_results(all_results)
    
    # 打印摘要
    print_summary(all_results)

def save_results(results: List[OrchestrationResult]):
    """保存结果到文件"""
    csv_path = os.path.join(RESULTS_DIR, 'metrics.csv')
    with open(csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([
            'orchestrator', 'test_case', 'total_time_ms', 'memory_usage_mb',
            'success', 'steps_completed', 'error_message'
        ])
        
        for result in results:
            writer.writerow([
                result.orchestrator, result.test_case, result.total_time_ms,
                result.memory_usage_mb, result.success, result.steps_completed,
                result.error_message or ''
            ])
    
    print(f"结果已保存到: {csv_path}")

def print_summary(results: List[OrchestrationResult]):
    """打印测试摘要"""
    print("\n" + "="*60)
    print("E4 编排对比真实测试摘要")
    print("="*60)
    
    # 按编排器分组
    orchestrators = {}
    for result in results:
        if result.orchestrator not in orchestrators:
            orchestrators[result.orchestrator] = []
        orchestrators[result.orchestrator].append(result)
    
    for orch_name, orch_results in orchestrators.items():
        successful = [r for r in orch_results if r.success]
        
        print(f"\n{orch_name.upper()} 性能:")
        print(f"  成功率: {len(successful)}/{len(orch_results)} ({len(successful)/len(orch_results):.1%})")
        
        if successful:
            avg_time = sum(r.total_time_ms for r in successful) / len(successful)
            avg_memory = sum(r.memory_usage_mb for r in successful) / len(successful)
            avg_steps = sum(r.steps_completed for r in successful) / len(successful)
            
            print(f"  平均处理时间: {avg_time:.1f}ms")
            print(f"  平均内存使用: {avg_memory:.1f}MB")
            print(f"  平均步骤数: {avg_steps:.1f}")

if __name__ == "__main__":
    main()
