#!/usr/bin/env python3
"""
真实规模向量检索测试
使用真实的历史文档数据测试Manticore vs PgVector的性能
数据：89个文档，575,384字符
"""

import os
import time
import json
import logging
import glob
import psycopg
import numpy as np
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from sentence_transformers import SentenceTransformer
import manticoresearch

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RetrievalResult:
    """检索结果"""
    engine: str
    document_count: int
    total_characters: int
    indexing_time_ms: float
    query_time_ms: float
    total_time_ms: float
    throughput_docs_per_sec: float
    success_rate: float
    error_count: int = 0

def load_real_documents(test_contents_dir: str = "test_contents") -> List[Dict[str, Any]]:
    """加载真实文档数据"""
    documents = []
    
    if not os.path.exists(test_contents_dir):
        logger.error(f"测试内容目录不存在: {test_contents_dir}")
        return documents
    
    md_files = glob.glob(os.path.join(test_contents_dir, "*.md"))
    logger.info(f"找到 {len(md_files)} 个Markdown文件")
    
    for file_path in md_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            doc_id = os.path.basename(file_path).replace('.md', '')
            documents.append({
                'id': doc_id,
                'title': doc_id,
                'content': content,
                'file_path': file_path,
                'char_count': len(content)
            })
            
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
    
    total_chars = sum(doc['char_count'] for doc in documents)
    logger.info(f"成功加载 {len(documents)} 个文档，总计 {total_chars:,} 字符")
    
    return documents

def generate_embeddings(documents: List[Dict[str, Any]], model: SentenceTransformer) -> List[Tuple[str, List[float]]]:
    """生成文档嵌入"""
    logger.info("开始生成文档嵌入...")
    
    embeddings = []
    batch_size = 10  # 批量处理以提高效率
    
    for i in range(0, len(documents), batch_size):
        batch_docs = documents[i:i + batch_size]
        batch_texts = [doc['content'][:2000] for doc in batch_docs]  # 限制长度
        batch_ids = [doc['id'] for doc in batch_docs]
        
        logger.info(f"处理批次 {i//batch_size + 1}/{(len(documents) + batch_size - 1)//batch_size}")
        
        batch_embeddings = model.encode(batch_texts, show_progress_bar=False)
        
        for doc_id, embedding in zip(batch_ids, batch_embeddings):
            embeddings.append((doc_id, embedding.tolist()))
    
    logger.info(f"生成了 {len(embeddings)} 个文档嵌入")
    return embeddings

class PgVectorEngine:
    """PostgreSQL + pgvector 引擎"""
    
    def __init__(self, conn_str: str = "postgresql://pguser:pgpass@localhost:5433/pgdb"):
        self.conn_str = conn_str
        logger.info("初始化PgVector引擎")
        
        # 测试连接
        try:
            with psycopg.connect(self.conn_str) as conn:
                with conn.cursor() as cur:
                    cur.execute("SELECT 1")
            logger.info("✓ PostgreSQL连接成功")
        except Exception as e:
            logger.error(f"PostgreSQL连接失败: {e}")
            raise
    
    def setup_table(self):
        """设置向量表"""
        try:
            with psycopg.connect(self.conn_str) as conn:
                with conn.cursor() as cur:
                    # 创建扩展
                    cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
                    
                    # 删除现有表
                    cur.execute("DROP TABLE IF EXISTS real_documents;")
                    
                    # 创建表
                    cur.execute("""
                        CREATE TABLE real_documents (
                            id SERIAL PRIMARY KEY,
                            doc_id VARCHAR(255) UNIQUE NOT NULL,
                            embedding vector(384)
                        );
                    """)
                    
                    # 创建索引
                    cur.execute("""
                        CREATE INDEX real_documents_embedding_idx 
                        ON real_documents USING ivfflat (embedding vector_cosine_ops) 
                        WITH (lists = 100);
                    """)
                    
                    conn.commit()
            
            logger.info("✓ PgVector表设置完成")
            
        except Exception as e:
            logger.error(f"PgVector表设置失败: {e}")
            raise
    
    def index_documents(self, embeddings: List[Tuple[str, List[float]]]) -> float:
        """索引文档"""
        start_time = time.time()
        
        try:
            with psycopg.connect(self.conn_str) as conn:
                with conn.cursor() as cur:
                    for doc_id, embedding in embeddings:
                        cur.execute("""
                            INSERT INTO real_documents (doc_id, embedding)
                            VALUES (%s, %s)
                        """, (doc_id, embedding))
                    
                    conn.commit()
            
            indexing_time = (time.time() - start_time) * 1000
            logger.info(f"PgVector索引完成: {len(embeddings)} 个文档, 耗时 {indexing_time:.2f}ms")
            return indexing_time
            
        except Exception as e:
            logger.error(f"PgVector索引失败: {e}")
            raise
    
    def search(self, query_embedding: List[float], top_k: int = 5) -> Tuple[List[str], float]:
        """搜索相似文档"""
        start_time = time.time()

        try:
            with psycopg.connect(self.conn_str) as conn:
                with conn.cursor() as cur:
                    # 将查询向量转换为字符串格式
                    query_vector_str = '[' + ','.join(map(str, query_embedding)) + ']'

                    cur.execute("""
                        SELECT doc_id, embedding <=> %s::vector as distance
                        FROM real_documents
                        ORDER BY embedding <=> %s::vector
                        LIMIT %s
                    """, (query_vector_str, query_vector_str, top_k))

                    results = cur.fetchall()
                    doc_ids = [row[0] for row in results]

            search_time = (time.time() - start_time) * 1000
            return doc_ids, search_time

        except Exception as e:
            logger.error(f"PgVector搜索失败: {e}")
            return [], 0

class ManticoreEngine:
    """Manticore Search 引擎"""

    def __init__(self, host: str = "localhost", port: int = 9308):
        self.host = host
        self.port = port
        logger.info(f"初始化Manticore引擎 (host={host}, port={port})")

        # 初始化客户端
        try:
            configuration = manticoresearch.Configuration(
                host=f"http://{host}:{port}"
            )
            self.api_client = manticoresearch.ApiClient(configuration)
            self.utils_api = manticoresearch.UtilsApi(self.api_client)
            self.search_api = manticoresearch.SearchApi(self.api_client)
            self.index_api = manticoresearch.IndexApi(self.api_client)

            # 测试连接
            self.utils_api.sql('SHOW TABLES')
            logger.info("✓ Manticore连接成功")
        except Exception as e:
            logger.error(f"Manticore连接失败: {e}")
            raise
    
    def setup_table(self):
        """设置向量表"""
        try:
            # 删除现有表
            try:
                self.utils_api.sql("DROP TABLE IF EXISTS real_documents")
            except:
                pass  # 表可能不存在

            # 创建表
            self.utils_api.sql("""
                CREATE TABLE real_documents (
                    doc_id string,
                    embedding float_vector knn_type='hnsw' knn_dims='384' hnsw_similarity='cosine'
                )
            """)

            logger.info("✓ Manticore表设置完成")

        except Exception as e:
            logger.error(f"Manticore表设置失败: {e}")
            raise
    
    def index_documents(self, embeddings: List[Tuple[str, List[float]]]) -> float:
        """索引文档"""
        start_time = time.time()

        try:
            for doc_id, embedding in embeddings:
                # 使用INSERT API
                doc = {
                    'doc_id': doc_id,
                    'embedding': embedding
                }
                self.index_api.insert({
                    'table': 'real_documents',
                    'doc': doc
                })

            indexing_time = (time.time() - start_time) * 1000
            logger.info(f"Manticore索引完成: {len(embeddings)} 个文档, 耗时 {indexing_time:.2f}ms")
            return indexing_time

        except Exception as e:
            logger.error(f"Manticore索引失败: {e}")
            raise
    
    def search(self, query_embedding: List[float], top_k: int = 5) -> Tuple[List[str], float]:
        """搜索相似文档"""
        start_time = time.time()

        try:
            # 使用KNN搜索
            search_request = {
                'table': 'real_documents',
                'knn': {
                    'field': 'embedding',
                    'query_vector': query_embedding,
                    'k': top_k
                }
            }

            response = self.search_api.search(search_request)

            doc_ids = []
            if hasattr(response, 'hits') and response.hits:
                for hit in response.hits:
                    if hasattr(hit, '_source') and 'doc_id' in hit._source:
                        doc_ids.append(hit._source['doc_id'])

            search_time = (time.time() - start_time) * 1000
            return doc_ids, search_time

        except Exception as e:
            logger.error(f"Manticore搜索失败: {e}")
            return [], 0

def run_real_scale_retrieval_test():
    """运行真实规模向量检索测试"""
    logger.info("开始真实规模向量检索测试")
    
    # 加载真实文档
    documents = load_real_documents()
    
    if not documents:
        logger.error("没有加载到文档，测试终止")
        return
    
    # 初始化嵌入模型
    logger.info("初始化嵌入模型...")
    model = SentenceTransformer('all-MiniLM-L6-v2')
    
    # 生成嵌入
    embeddings = generate_embeddings(documents, model)
    
    # 生成查询嵌入
    test_queries = [
        "中国历史发展过程",
        "朝代政治制度变化",
        "经济发展与社会变迁",
        "文化思想传承",
        "军事战争影响"
    ]
    
    query_embeddings = model.encode(test_queries, show_progress_bar=False)
    
    results = []
    
    # 测试PgVector
    logger.info("测试PgVector性能...")
    try:
        pgvector_engine = PgVectorEngine()
        pgvector_engine.setup_table()
        
        # 索引文档
        pgvector_indexing_time = pgvector_engine.index_documents(embeddings)
        
        # 执行查询
        total_query_time = 0
        successful_queries = 0
        
        for i, query_emb in enumerate(query_embeddings):
            try:
                doc_ids, query_time = pgvector_engine.search(query_emb.tolist(), top_k=5)
                total_query_time += query_time
                successful_queries += 1
                logger.info(f"PgVector查询 {i+1}: {query_time:.2f}ms, 找到 {len(doc_ids)} 个结果")
            except Exception as e:
                logger.error(f"PgVector查询 {i+1} 失败: {e}")
        
        avg_query_time = total_query_time / successful_queries if successful_queries > 0 else 0
        total_time = pgvector_indexing_time + total_query_time
        
        results.append(RetrievalResult(
            engine="PgVector",
            document_count=len(documents),
            total_characters=sum(doc['char_count'] for doc in documents),
            indexing_time_ms=pgvector_indexing_time,
            query_time_ms=avg_query_time,
            total_time_ms=total_time,
            throughput_docs_per_sec=len(documents) / (total_time / 1000) if total_time > 0 else 0,
            success_rate=successful_queries / len(test_queries)
        ))
        
    except Exception as e:
        logger.error(f"PgVector测试失败: {e}")
    
    # 测试Manticore
    logger.info("测试Manticore性能...")
    try:
        manticore_engine = ManticoreEngine()
        manticore_engine.setup_table()
        
        # 索引文档
        manticore_indexing_time = manticore_engine.index_documents(embeddings)
        
        # 执行查询
        total_query_time = 0
        successful_queries = 0
        
        for i, query_emb in enumerate(query_embeddings):
            try:
                doc_ids, query_time = manticore_engine.search(query_emb.tolist(), top_k=5)
                total_query_time += query_time
                successful_queries += 1
                logger.info(f"Manticore查询 {i+1}: {query_time:.2f}ms, 找到 {len(doc_ids)} 个结果")
            except Exception as e:
                logger.error(f"Manticore查询 {i+1} 失败: {e}")
        
        avg_query_time = total_query_time / successful_queries if successful_queries > 0 else 0
        total_time = manticore_indexing_time + total_query_time
        
        results.append(RetrievalResult(
            engine="Manticore",
            document_count=len(documents),
            total_characters=sum(doc['char_count'] for doc in documents),
            indexing_time_ms=manticore_indexing_time,
            query_time_ms=avg_query_time,
            total_time_ms=total_time,
            throughput_docs_per_sec=len(documents) / (total_time / 1000) if total_time > 0 else 0,
            success_rate=successful_queries / len(test_queries)
        ))
        
    except Exception as e:
        logger.error(f"Manticore测试失败: {e}")
    
    # 保存结果
    summary = {
        'test_overview': {
            'total_documents': len(documents),
            'total_characters': sum(doc['char_count'] for doc in documents),
            'test_queries': len(test_queries),
            'embedding_dimension': 384
        },
        'results': {}
    }
    
    for result in results:
        summary['results'][result.engine.lower()] = {
            'indexing_time_ms': result.indexing_time_ms,
            'avg_query_time_ms': result.query_time_ms,
            'total_time_ms': result.total_time_ms,
            'throughput_docs_per_sec': result.throughput_docs_per_sec,
            'success_rate': result.success_rate
        }
    
    # 计算性能对比
    if len(results) == 2:
        pgvector_result = next(r for r in results if r.engine == "PgVector")
        manticore_result = next(r for r in results if r.engine == "Manticore")
        
        summary['performance_comparison'] = {
            'indexing_speed_improvement': ((pgvector_result.indexing_time_ms - manticore_result.indexing_time_ms) / pgvector_result.indexing_time_ms * 100) if pgvector_result.indexing_time_ms > 0 else 0,
            'query_speed_improvement': ((pgvector_result.query_time_ms - manticore_result.query_time_ms) / pgvector_result.query_time_ms * 100) if pgvector_result.query_time_ms > 0 else 0,
            'total_speed_improvement': ((pgvector_result.total_time_ms - manticore_result.total_time_ms) / pgvector_result.total_time_ms * 100) if pgvector_result.total_time_ms > 0 else 0
        }
    
    output_file = 'scripts/real_scale_retrieval_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    logger.info(f"结果已保存到: {output_file}")
    
    # 打印摘要
    print("\n" + "="*80)
    print("真实规模向量检索测试结果")
    print("="*80)
    print(f"测试数据: {len(documents)} 个文档, {sum(doc['char_count'] for doc in documents):,} 字符")
    print(f"查询数量: {len(test_queries)}")
    
    for result in results:
        print(f"\n{result.engine} 性能:")
        print(f"  索引时间: {result.indexing_time_ms:.2f}ms")
        print(f"  平均查询时间: {result.query_time_ms:.2f}ms")
        print(f"  总时间: {result.total_time_ms:.2f}ms")
        print(f"  吞吐量: {result.throughput_docs_per_sec:.1f} docs/sec")
        print(f"  成功率: {result.success_rate:.1%}")
    
    if 'performance_comparison' in summary:
        comp = summary['performance_comparison']
        print(f"\n性能对比:")
        if comp['total_speed_improvement'] > 0:
            print(f"  Manticore比PgVector快 {comp['total_speed_improvement']:.1f}%")
        else:
            print(f"  PgVector比Manticore快 {-comp['total_speed_improvement']:.1f}%")
    
    print("="*80)

if __name__ == "__main__":
    run_real_scale_retrieval_test()
