#!/usr/bin/env python3
"""
详细对比分析：规则化 vs LLM 的实际效果

基于真实对话数据，深入分析各种方法的实际表现
"""

import os
import glob
import json
from eval_e3_real_extraction import RuleBasedExtractor, OpenAIExtractor

def load_all_dialogues():
    """加载所有对话文件"""
    dialogues_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'real_dialogues')
    files = glob.glob(os.path.join(dialogues_dir, '*.md'))
    
    dialogues = []
    for file_path in files:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        dialogues.append({
            'filename': os.path.basename(file_path),
            'content': content[:1500]  # 取前1500字符
        })
    
    return dialogues

def compare_extraction_quality():
    """对比抽取质量"""
    print("=" * 80)
    print("🔬 深度对比：规则化 vs LLM 抽取质量")
    print("=" * 80)
    
    dialogues = load_all_dialogues()
    rule_extractor = RuleBasedExtractor()
    
    try:
        llm_extractor = OpenAIExtractor()
        llm_available = True
    except:
        llm_available = False
        print("⚠️ LLM 抽取器不可用，仅展示规则化结果")
    
    for i, dialogue in enumerate(dialogues):
        print(f"\n📄 文档 {i+1}: {dialogue['filename']}")
        print("-" * 60)
        print(f"内容预览: {dialogue['content'][:200]}...")
        print()
        
        # 规则化抽取
        rule_result = rule_extractor.extract(dialogue['content'], dialogue['filename'])
        print("📋 规则化抽取:")
        print(f"  主题: {rule_result.topic}")
        print(f"  关键点: {rule_result.key_points[:2]}")  # 只显示前2个
        print(f"  实体: {[e['text'] for e in rule_result.entities[:3]]}")  # 只显示前3个
        print(f"  摘要: {rule_result.summary[:100]}...")
        print(f"  耗时: {rule_result.processing_time_ms:.1f}ms")
        
        # LLM 抽取
        if llm_available:
            llm_result = llm_extractor.extract(dialogue['content'], dialogue['filename'])
            print("\n🤖 LLM 抽取:")
            print(f"  主题: {llm_result.topic}")
            print(f"  关键点: {llm_result.key_points[:2]}")  # 只显示前2个
            print(f"  实体: {[e['text'] for e in llm_result.entities[:3]]}")  # 只显示前3个
            print(f"  摘要: {llm_result.summary[:100]}...")
            print(f"  耗时: {llm_result.processing_time_ms:.1f}ms")
            print(f"  置信度: {llm_result.confidence_score}")
            
            # 质量对比分析
            print("\n🔍 质量对比:")
            print(f"  主题准确性: {'LLM更准确' if llm_result.topic != '其他' and rule_result.topic == '其他' else '需人工判断'}")
            print(f"  速度对比: 规则化快 {llm_result.processing_time_ms / rule_result.processing_time_ms:.0f} 倍")
            print(f"  关键点数量: 规则化 {len(rule_result.key_points)} vs LLM {len(llm_result.key_points)}")
            print(f"  实体数量: 规则化 {len(rule_result.entities)} vs LLM {len(llm_result.entities)}")

def analyze_splitting_effectiveness():
    """分析分割效果"""
    print("\n" + "=" * 80)
    print("✂️ 文本分割效果分析")
    print("=" * 80)
    
    from eval_splitting_retrieval import NaiveSplitter, SemanticSplitter
    
    dialogues = load_all_dialogues()
    naive_splitter = NaiveSplitter(chunk_size=400, overlap=50)
    semantic_splitter = SemanticSplitter(target_size=400, max_size=600)
    
    for i, dialogue in enumerate(dialogues):
        print(f"\n📄 文档 {i+1}: {dialogue['filename']}")
        print("-" * 60)
        
        # 朴素分割
        naive_chunks = naive_splitter.split(dialogue['filename'], dialogue['content'])
        print(f"🔪 朴素分割: {len(naive_chunks)} 个块")
        for j, chunk in enumerate(naive_chunks[:2]):
            print(f"  块{j+1} ({len(chunk.content)}字符): {chunk.content[:80]}...")
            # 检查是否在句子中间断开
            if chunk.content[-1] not in '。！？':
                print(f"    ⚠️ 可能在句子中间断开")
        
        # 语义分割
        semantic_chunks = semantic_splitter.split(dialogue['filename'], dialogue['content'])
        print(f"\n🧠 语义分割: {len(semantic_chunks)} 个块")
        for j, chunk in enumerate(semantic_chunks[:2]):
            print(f"  块{j+1} ({len(chunk.content)}字符): {chunk.content[:80]}...")
            # 检查语义完整性
            if chunk.content[-1] in '。！？':
                print(f"    ✅ 在句子边界结束")
        
        print(f"\n📊 对比:")
        print(f"  块数量: 朴素 {len(naive_chunks)} vs 语义 {len(semantic_chunks)}")
        avg_naive = sum(len(c.content) for c in naive_chunks) / len(naive_chunks)
        avg_semantic = sum(len(c.content) for c in semantic_chunks) / len(semantic_chunks)
        print(f"  平均长度: 朴素 {avg_naive:.0f} vs 语义 {avg_semantic:.0f}")

def test_retrieval_simulation():
    """模拟检索测试"""
    print("\n" + "=" * 80)
    print("🔍 检索效果模拟测试")
    print("=" * 80)
    
    dialogues = load_all_dialogues()
    
    # 模拟用户查询
    test_queries = [
        "大脑健身的主要好处是什么？",
        "运动如何影响记忆力？",
        "压力对大脑有什么影响？",
        "如何通过运动改善大脑功能？"
    ]
    
    for dialogue in dialogues:
        print(f"\n📄 文档: {dialogue['filename']}")
        print(f"内容: {dialogue['content'][:150]}...")
        
        for query in test_queries:
            print(f"\n🔍 查询: {query}")
            
            # 简单的关键词匹配
            query_words = [w for w in query.replace('？', '').replace('什么', '').replace('如何', '').split() if len(w) > 1]
            
            # 在文档中查找相关句子
            sentences = dialogue['content'].split('。')
            relevant_sentences = []
            
            for sentence in sentences:
                score = sum(1 for word in query_words if word in sentence)
                if score > 0:
                    relevant_sentences.append((sentence.strip(), score))
            
            # 按相关性排序
            relevant_sentences.sort(key=lambda x: x[1], reverse=True)
            
            if relevant_sentences:
                print(f"  找到 {len(relevant_sentences)} 个相关句子:")
                for j, (sentence, score) in enumerate(relevant_sentences[:2]):
                    print(f"    {j+1}. (相关度:{score}) {sentence[:100]}...")
            else:
                print("  ❌ 未找到相关内容")

def main():
    """主函数"""
    print("🎯 详细对比分析")
    print("基于真实对话数据的深度分析")
    print()
    
    try:
        # 抽取质量对比
        compare_extraction_quality()
        
        # 分割效果分析
        analyze_splitting_effectiveness()
        
        # 检索效果模拟
        test_retrieval_simulation()
        
        print("\n" + "=" * 80)
        print("📋 总结建议")
        print("=" * 80)
        print("基于实际结果:")
        print("1. 📝 文本分割: 语义分割在句子边界处理上更好")
        print("2. 🔧 结构化抽取: LLM 质量明显优于规则化，但速度慢很多")
        print("3. 🔍 检索效果: 需要结合实际业务场景权衡速度 vs 质量")
        print("4. 💡 建议: 对质量要求高的场景使用 LLM，对速度要求高的场景考虑规则化")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
