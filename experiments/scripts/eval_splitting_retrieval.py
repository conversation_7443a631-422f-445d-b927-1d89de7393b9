#!/usr/bin/env python3
"""
E2 文本分割客观评测（使用 test_contents 语料）

目标：在无人工标注的前提下，构造“自监督”查询集（从文档内句子采样），
对比朴素切分 vs 语义切分在检索环节的客观指标：Recall@K、nDCG@K、Top1 覆盖率，
并输出分割形态与跨标题污染率（若存在 markdown 标题）。

说明：
- 支持两种检索模式：
  1. 轻量模式：字符 n-gram + 余弦相似度（无外部依赖）
  2. 向量模式：sentence-transformers + 向量相似度（更准确）
- 语料来源：experiments/test_contents/*.md
- 输出目录：experiments/E2_text_splitting/results/
"""

import os
import re
import csv
import json
import time
import math
import glob
import argparse
import numpy as np
from collections import Counter, defaultdict
from dataclasses import dataclass
from typing import List, Tuple, Dict, Any, Optional

# 尝试导入依赖
try:
    from sentence_transformers import SentenceTransformer
    LOCAL_VECTOR_AVAILABLE = True
except ImportError:
    LOCAL_VECTOR_AVAILABLE = False

try:
    import openai
    from dotenv import load_dotenv
    OPENAI_AVAILABLE = True
    # 加载环境变量
    load_dotenv()
except ImportError:
    OPENAI_AVAILABLE = False

VECTOR_AVAILABLE = LOCAL_VECTOR_AVAILABLE or OPENAI_AVAILABLE

ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 默认语料目录改为真实对话，可通过 --corpus_dir 覆盖
DEFAULT_CORPUS_DIR = os.path.join(ROOT, 'real_dialogues')
RESULTS_DIR = os.path.join(ROOT, 'E2_text_splitting', 'results')
os.makedirs(RESULTS_DIR, exist_ok=True)

# 默认向量模型
DEFAULT_LOCAL_MODEL = 'all-MiniLM-L6-v2'
DEFAULT_OPENAI_MODEL = 'text-embedding-3-small'

def get_openai_embeddings(texts: List[str], model: str = DEFAULT_OPENAI_MODEL) -> np.ndarray:
    """使用 OpenAI API 获取文本嵌入"""
    if not OPENAI_AVAILABLE:
        raise ImportError("OpenAI 不可用，请安装: pip install openai python-dotenv")

    # 配置 OpenAI 客户端
    client = openai.OpenAI(
        api_key=os.getenv('OPENAI_API_KEY'),
        base_url=os.getenv('OPENAI_BASE_URL')
    )

    # 批量处理，避免单次请求过大
    batch_size = 100
    all_embeddings = []

    print(f"使用 OpenAI 模型 {model} 生成嵌入向量...")
    for i in range(0, len(texts), batch_size):
        batch = texts[i:i + batch_size]
        print(f"处理批次 {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")

        try:
            response = client.embeddings.create(
                input=batch,
                model=model
            )
            batch_embeddings = [item.embedding for item in response.data]
            all_embeddings.extend(batch_embeddings)
        except Exception as e:
            print(f"OpenAI API 调用失败: {e}")
            raise

    return np.array(all_embeddings)


# --------------------------- 基础工具 ---------------------------

def char_ngrams(text: str, n: int = 2) -> Counter:
    s = re.sub(r"\s+", "", text)
    grams = [s[i:i+n] for i in range(max(0, len(s) - n + 1))]
    return Counter(grams)


def cosine_sim(a: Counter, b: Counter) -> float:
    if not a or not b:
        return 0.0
    # dot
    keys = set(a.keys()) & set(b.keys())
    dot = sum(a[k] * b[k] for k in keys)
    # norms
    na = math.sqrt(sum(v*v for v in a.values()))
    nb = math.sqrt(sum(v*v for v in b.values()))
    if na == 0 or nb == 0:
        return 0.0
    return dot / (na * nb)


def split_sentences(text: str) -> List[Tuple[str, int, int]]:
    """按中文句读切分，返回 (句子, start, end)（包含标点）。"""
    sentences = []
    pattern = re.compile(r'([^。！？\n\r]+[。！？]?)')
    pos = 0
    for m in pattern.finditer(text):
        sent = m.group(1)
        start = text.find(sent, pos)
        if start == -1:
            continue
        end = start + len(sent)
        pos = end
        s = sent.strip()
        if s:
            sentences.append((sent, start, end))
    return sentences


def find_headers(text: str) -> List[Tuple[int, int, str]]:
    """识别 markdown 标题，返回[(start_pos, end_pos, header_text)]。
    若没有标题，返回空列表。
    """
    headers = []
    offset = 0
    for line in text.splitlines(keepends=True):
        if line.lstrip().startswith('#'):
            start = offset
            end = offset + len(line)
            headers.append((start, end, line.strip()))
        offset += len(line)
    return headers


def segment_by_headers(text: str, headers: List[Tuple[int, int, str]]) -> List[Tuple[int, int, Optional[str]]]:
    """将文档按标题段落区间化，返回区间列表 [(seg_start, seg_end, header_text or None)]。
    若无标题，返回全段 [(0, len(text), None)]。
    """
    if not headers:
        return [(0, len(text), None)]
    segments = []
    # 标题起点序列
    starts = [h[0] for h in headers]
    texts = [h[2] for h in headers]
    for i, st in enumerate(starts):
        seg_start = st
        seg_end = starts[i+1] if i + 1 < len(starts) else len(text)
        segments.append((seg_start, seg_end, texts[i]))
    return segments


def which_segment(pos: int, segments: List[Tuple[int, int, Optional[str]]]) -> int:
    for idx, (s, e, _) in enumerate(segments):
        if s <= pos < e:
            return idx
    return -1


# --------------------------- 分割器 ---------------------------

@dataclass
class Chunk:
    strategy: str
    doc_id: str
    chunk_id: int
    start: int
    end: int
    content: str


class NaiveSplitter:
    def __init__(self, chunk_size: int = 500, overlap: int = 50):
        self.chunk_size = chunk_size
        self.overlap = overlap

    def split(self, doc_id: str, text: str) -> List[Chunk]:
        chunks: List[Chunk] = []
        start = 0
        cid = 0
        while start < len(text):
            end = min(start + self.chunk_size, len(text))
            # 向前对齐到句读
            if end < len(text):
                dot = text.rfind('。', start, end)
                if dot > start:
                    end = dot + 1
            content = text[start:end]
            if content.strip():
                chunks.append(Chunk('naive', doc_id, cid, start, end, content))
                cid += 1
            if end >= len(text):
                break
            start = max(start + self.chunk_size - self.overlap, end)
        return chunks


class SemanticSplitter:
    def __init__(self, target_size: int = 500, max_size: int = 800):
        self.target_size = target_size
        self.max_size = max_size

    def split(self, doc_id: str, text: str) -> List[Chunk]:
        sentences = split_sentences(text)
        chunks: List[Chunk] = []
        current: List[Tuple[str, int, int]] = []
        cur_start = 0
        cur_len = 0
        cid = 0
        for sent, s, e in sentences:
            if not current:
                cur_start = s
            if cur_len + len(sent) > self.target_size and cur_len > 0:
                content = ''.join([x[0] for x in current]).strip()
                chunks.append(Chunk('semantic', doc_id, cid, cur_start, cur_start + len(content), content))
                cid += 1
                current = [(sent, s, e)]
                cur_start = s
                cur_len = len(sent)
            else:
                current.append((sent, s, e))
                cur_len += len(sent)
            if cur_len > self.max_size:
                content = ''.join([x[0] for x in current]).strip()
                chunks.append(Chunk('semantic', doc_id, cid, cur_start, cur_start + len(content), content))
                cid += 1
                current = []
                cur_len = 0
        if current:
            content = ''.join([x[0] for x in current]).strip()
            chunks.append(Chunk('semantic', doc_id, cid, cur_start, cur_start + len(content), content))
        return chunks


# --------------------------- 检索与评测 ---------------------------

def build_index(chunks: List[Chunk], mode: str = 'ngram', model_name: str = DEFAULT_OPENAI_MODEL, ngram: int = 2) -> Tuple[List[Chunk], Any]:
    """构建索引，支持 ngram、local_vector 和 openai_vector 三种模式"""
    if mode == 'vector':
        if OPENAI_AVAILABLE:
            print(f"使用 OpenAI 向量模型: {model_name}")
            texts = [c.content for c in chunks]
            vectors = get_openai_embeddings(texts, model_name)
            return chunks, vectors
        elif LOCAL_VECTOR_AVAILABLE:
            print(f"使用本地向量模型: {model_name}")
            model = SentenceTransformer(model_name or DEFAULT_LOCAL_MODEL)
            texts = [c.content for c in chunks]
            vectors = model.encode(texts, convert_to_tensor=False, show_progress_bar=True)
            return chunks, vectors
        else:
            print("警告: 向量模型不可用，回退到 n-gram 模式")
            mode = 'ngram'

    # n-gram 模式
    vectors = [char_ngrams(c.content, n=ngram) for c in chunks]
    return chunks, vectors


def search(query: str, index: Tuple[List[Chunk], Any], top_k: int = 10, mode: str = 'ngram', model_name: str = DEFAULT_OPENAI_MODEL, ngram: int = 2) -> List[Tuple[int, float]]:
    """检索，支持 ngram 和 vector 两种模式"""
    chunks, vectors = index

    if mode == 'vector' and isinstance(vectors, np.ndarray):
        # 向量检索模式
        if OPENAI_AVAILABLE:
            # 使用 OpenAI API 获取查询向量
            query_vector = get_openai_embeddings([query], model_name)[0]
        elif LOCAL_VECTOR_AVAILABLE:
            # 使用本地模型获取查询向量
            model = SentenceTransformer(model_name or DEFAULT_LOCAL_MODEL)
            query_vector = model.encode([query], convert_to_tensor=False)[0]
        else:
            # 回退到 n-gram
            qv = char_ngrams(query, n=ngram)
            sims = [(i, cosine_sim(qv, vectors[i])) for i in range(len(chunks))]
            sims.sort(key=lambda x: x[1], reverse=True)
            return sims[:top_k]

        # 计算余弦相似度
        similarities = np.dot(vectors, query_vector)
        # 归一化
        norms_chunks = np.linalg.norm(vectors, axis=1)
        norm_query = np.linalg.norm(query_vector)
        similarities = similarities / (norms_chunks * norm_query + 1e-8)

        # 排序并返回 top_k
        indices = np.argsort(similarities)[::-1][:top_k]
        return [(int(idx), float(similarities[idx])) for idx in indices]
    else:
        # n-gram 模式
        qv = char_ngrams(query, n=ngram)
        sims = [(i, cosine_sim(qv, vectors[i])) for i in range(len(chunks))]
        sims.sort(key=lambda x: x[1], reverse=True)
        return sims[:top_k]


def search_with_precomputed_query(query_vector: np.ndarray, index: Tuple[List[Chunk], np.ndarray], top_k: int = 10) -> List[Tuple[int, float]]:
    """使用预计算的查询向量进行检索"""
    chunks, vectors = index

    # 计算余弦相似度
    similarities = np.dot(vectors, query_vector)
    # 归一化
    norms_chunks = np.linalg.norm(vectors, axis=1)
    norm_query = np.linalg.norm(query_vector)
    similarities = similarities / (norms_chunks * norm_query + 1e-8)

    # 排序并返回 top_k
    indices = np.argsort(similarities)[::-1][:top_k]
    return [(int(idx), float(similarities[idx])) for idx in indices]


def overlap_len(a: Tuple[int, int], b: Tuple[int, int]) -> int:
    s1, e1 = a
    s2, e2 = b
    s = max(s1, s2)
    e = min(e1, e2)
    return max(0, e - s)


def dcg_at_k(gains: List[int], k: int) -> float:
    s = 0.0
    for i in range(min(k, len(gains))):
        s += (2**gains[i] - 1) / math.log2(i + 2)
    return s


def ndcg_at_k(gains: List[int], k: int) -> float:
    dcg = dcg_at_k(gains, k)
    ideal = dcg_at_k(sorted(gains, reverse=True), k)
    return (dcg / ideal) if ideal > 0 else 0.0


def eval_strategy(strategy: str,
                  all_chunks: List[Chunk],
                  header_segments_by_doc: Dict[str, List[Tuple[int, int, Optional[str]]]],
                  queries: List[Dict[str, Any]],
                  top_ks: List[int] = [1, 3, 5, 10],
                  mode: str = 'ngram',
                  model_name: str = DEFAULT_OPENAI_MODEL,
                  ngram: int = 2) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    # 构建索引（跨文档）
    t0 = time.time()
    index = build_index(all_chunks, mode=mode, model_name=model_name, ngram=ngram)
    index_ms = (time.time() - t0) * 1000

    # 批量生成查询向量（如果是向量模式）
    query_vectors = None
    if mode == 'vector' and isinstance(index[1], np.ndarray):
        if OPENAI_AVAILABLE:
            print("批量生成查询向量...")
            query_texts = [q['text'] for q in queries]
            query_vectors = get_openai_embeddings(query_texts, model_name)
        elif LOCAL_VECTOR_AVAILABLE:
            print("批量生成查询向量...")
            model = SentenceTransformer(model_name or DEFAULT_LOCAL_MODEL)
            query_texts = [q['text'] for q in queries]
            query_vectors = model.encode(query_texts, convert_to_tensor=False, show_progress_bar=True)

    # 查询评测
    per_query_rows: List[Dict[str, Any]] = []
    recalls = {k: [] for k in top_ks}
    ndcgs = {k: [] for k in top_ks}
    coverages_top1: List[float] = []

    for i, q in enumerate(queries):
        q_text = q['text']
        q_doc = q['doc_id']
        q_span = (q['start'], q['end'])

        # 使用预计算的查询向量或实时计算
        if query_vectors is not None:
            hits = search_with_precomputed_query(query_vectors[i], index, top_k=max(top_ks))
        else:
            hits = search(q_text, index, top_k=max(top_ks), mode=mode, model_name=model_name, ngram=ngram)
        # 构造 gains（只要 overlap>0 记 1，否则 0）
        gains = []
        first_overlap_coverage = 0.0
        first_rank = None
        for rank, (idx, sim) in enumerate(hits, start=1):
            ch = index[0][idx]
            ov = overlap_len((ch.start, ch.end), q_span) if (ch.doc_id == q_doc) else 0
            rel = 1 if ov > 0 else 0
            gains.append(rel)
            if first_rank is None and rel == 1:
                first_rank = rank
                first_overlap_coverage = ov / max(1, (q_span[1] - q_span[0]))

        row = {
            'strategy': strategy,
            'query_id': q['id'],
            'doc_id': q_doc,
            'span_start': q_span[0],
            'span_end': q_span[1],
            'first_hit_rank': first_rank if first_rank is not None else 0,
            'top1_coverage': first_overlap_coverage if first_rank is not None else 0.0,
        }
        for k in top_ks:
            rec_k = 1.0 if any(gains[:k]) else 0.0
            ndcg_k = ndcg_at_k(gains, k)
            row[f'recall@{k}'] = rec_k
            row[f'ndcg@{k}'] = ndcg_k
            recalls[k].append(rec_k)
            ndcgs[k].append(ndcg_k)
        coverages_top1.append(row['top1_coverage'])
        per_query_rows.append(row)

    # 形态与污染率（跨标题）
    chunk_lens = [len(c.content) for c in all_chunks]
    bleed_count = 0
    total = len(all_chunks)
    for c in all_chunks:
        segs = header_segments_by_doc.get(c.doc_id, [(0, float('inf'), None)])
        s_idx = which_segment(c.start, segs)
        e_idx = which_segment(c.end - 1, segs)
        if s_idx != -1 and e_idx != -1 and s_idx != e_idx:
            bleed_count += 1
    bleed_rate = (bleed_count / total) if total > 0 else 0.0

    summary = {
        'strategy': strategy,
        'index_time_ms': index_ms,
        'avg_chunk_size': (sum(chunk_lens) / len(chunk_lens)) if chunk_lens else 0,
        'total_chunks': total,
        'bleed_rate': bleed_rate,
        'avg_top1_coverage': sum(coverages_top1) / len(coverages_top1) if coverages_top1 else 0.0,
    }
    for k in top_ks:
        summary[f'avg_recall@{k}'] = sum(recalls[k]) / len(recalls[k]) if recalls[k] else 0.0
        summary[f'avg_ndcg@{k}'] = sum(ndcgs[k]) / len(ndcgs[k]) if ndcgs[k] else 0.0

    return per_query_rows, summary


def build_queries(text: str, doc_id: str, max_per_doc: int = 5) -> List[Dict[str, Any]]:
    sents = split_sentences(text)
    # 选择中等长度句子作为“自监督”查询
    candidates = [(s, st, en) for (s, st, en) in sents if 16 <= len(s.strip()) <= 80]
    # 去重与限量
    queries = []
    used_ranges = []
    for (s, st, en) in candidates:
        if len(queries) >= max_per_doc:
            break
        # 避免重叠区域
        if any(overlap_len((st, en), r) > 0 for r in used_ranges):
            continue
        queries.append({'id': f'{doc_id}#{len(queries)}', 'text': s.strip(), 'doc_id': doc_id, 'start': st, 'end': en})
        used_ranges.append((st, en))
    return queries


def load_corpus(corpus_dir: str) -> List[Dict[str, Any]]:
    docs = []
    for path in sorted(glob.glob(os.path.join(corpus_dir, '*.md'))):
        try:
            with open(path, 'r', encoding='utf-8') as f:
                text = f.read()
            doc_id = os.path.splitext(os.path.basename(path))[0]
            docs.append({'doc_id': doc_id, 'path': path, 'text': text})
        except Exception:
            continue
    return docs


def main():
    parser = argparse.ArgumentParser(description='E2 文本分割检索评测')
    parser.add_argument('--mode', choices=['ngram', 'vector'], default='ngram',
                        help='检索模式: ngram (轻量) 或 vector (准确)')
    parser.add_argument('--model', default=DEFAULT_OPENAI_MODEL,
                        help=f'向量模型名称 (默认: {DEFAULT_OPENAI_MODEL})')
    parser.add_argument('--ngram', type=int, default=2,
                        help='n-gram 大小 (仅 ngram 模式)')
    parser.add_argument('--corpus_dir', default=DEFAULT_CORPUS_DIR,
                        help='语料目录，默认 experiments/real_dialogues')
    args = parser.parse_args()

    # 检查向量模式可用性
    if args.mode == 'vector' and not VECTOR_AVAILABLE:
        print("错误: sentence-transformers 不可用，请安装: pip install sentence-transformers")
        print("回退到 n-gram 模式")
        args.mode = 'ngram'

    docs = load_corpus(args.corpus_dir)
    if not docs:
        print(f'未在 {args.corpus_dir} 找到语料 .md 文件')
        return

    naive = NaiveSplitter(chunk_size=500, overlap=50)
    semantic = SemanticSplitter(target_size=500, max_size=800)

    naive_chunks: List[Chunk] = []
    semantic_chunks: List[Chunk] = []
    header_segments_by_doc: Dict[str, List[Tuple[int, int, Optional[str]]]] = {}
    all_queries: List[Dict[str, Any]] = []

    t0 = time.time()
    for d in docs:
        text = d['text']
        doc_id = d['doc_id']
        headers = find_headers(text)
        header_segments_by_doc[doc_id] = segment_by_headers(text, headers)

        # 分割
        t_s = time.time()
        ncs = naive.split(doc_id, text)
        naive_chunks.extend(ncs)
        t_sem = time.time()
        scs = semantic.split(doc_id, text)
        semantic_chunks.extend(scs)
        # 构造查询
        qs = build_queries(text, doc_id, max_per_doc=5)
        all_queries.extend(qs)
    split_ms = (time.time() - t0) * 1000

    # 评测
    print(f"使用 {args.mode} 模式进行评测...")
    rows_naive, sum_naive = eval_strategy('naive', naive_chunks, header_segments_by_doc, all_queries,
                                          mode=args.mode, model_name=args.model, ngram=args.ngram)
    rows_sem, sum_sem = eval_strategy('semantic', semantic_chunks, header_segments_by_doc, all_queries,
                                      mode=args.mode, model_name=args.model, ngram=args.ngram)

    # 写入 per-query 结果（根据模式命名）
    suffix = f"_{args.mode}" if args.mode != 'ngram' else ""
    per_query_path = os.path.join(RESULTS_DIR, f'metrics_eval{suffix}.csv')
    with open(per_query_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        header = ['strategy', 'query_id', 'doc_id', 'span_start', 'span_end', 'first_hit_rank', 'top1_coverage',
                  'recall@1', 'recall@3', 'recall@5', 'recall@10', 'ndcg@1', 'ndcg@3', 'ndcg@5', 'ndcg@10']
        writer.writerow(header)
        for r in rows_naive + rows_sem:
            writer.writerow([
                r['strategy'], r['query_id'], r['doc_id'], r['span_start'], r['span_end'],
                r['first_hit_rank'], f"{r['top1_coverage']:.4f}",
                f"{r.get('recall@1', 0):.4f}", f"{r.get('recall@3', 0):.4f}", f"{r.get('recall@5', 0):.4f}", f"{r.get('recall@10', 0):.4f}",
                f"{r.get('ndcg@1', 0):.4f}", f"{r.get('ndcg@3', 0):.4f}", f"{r.get('ndcg@5', 0):.4f}", f"{r.get('ndcg@10', 0):.4f}",
            ])

    # 汇总统计
    summary = {
        'mode': args.mode,
        'model_name': args.model if args.mode == 'vector' else None,
        'split_time_ms_all_docs': split_ms,
        'naive': sum_naive,
        'semantic': sum_sem,
        'queries': {'count': len(all_queries)}
    }
    summary_path = os.path.join(RESULTS_DIR, f'metrics_eval_summary{suffix}.json')
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)

    # 控制台摘要
    def fmt(s: Dict[str, Any]) -> str:
        return (
            f"chunks={s['total_chunks']}, avg_len={s['avg_chunk_size']:.1f}, bleed={s['bleed_rate']:.3f}, "
            f"R@5={s['avg_recall@5']:.3f}, nDCG@5={s['avg_ndcg@5']:.3f}, top1_cov={s['avg_top1_coverage']:.3f}"
        )
    print(f"\n====== E2 自监督检索评测（{args.mode} 模式）======")
    print(f"文档数: {len(docs)}, 查询数: {len(all_queries)}, 分割耗时: {split_ms:.1f} ms")
    print(f"语料目录: {args.corpus_dir}")
    if args.mode == 'vector':
        print(f"向量模型: {args.model}")
    print("Naive   ->", fmt(sum_naive))
    print("Semantic->", fmt(sum_sem))
    print(f"结果已写入: {per_query_path} 与 {summary_path}")


if __name__ == '__main__':
    main()
