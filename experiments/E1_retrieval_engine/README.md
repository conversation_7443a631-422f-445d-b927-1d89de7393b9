# E1 检索引擎 Bakeoff

目标：对比 pgvector、Manticore（可选 OpenSearch）的延迟、召回、资源占用与维护复杂度。

运行步骤：
1. 准备数据：推荐直接使用 `experiments/real_dialogues/*.md`（真实对话/讲解文），或依旧使用 `data/corpus/` 自定义语料；`run_experiment.py` 将自动从真实语料中抽取查询。
2. 配置参数：编辑 `config.yaml`（embedding 模型、TopK、过滤字段）。
3. 运行脚本：
   - 建索引：`scripts/index_pgvector.py`、`scripts/index_manticore.py`
   - 评估：`scripts/eval_pgvector.py`、`scripts/eval_manticore.py`
   - 快速本地对比（内置向量与模拟引擎）：
     - 合成数据：`python E1_retrieval_engine/run_experiment.py`
     - 真实对话：`python E1_retrieval_engine/run_experiment.py experiments/real_dialogues`
4. 结果输出：`results/metrics_pgvector.csv`、`results/metrics_manticore.csv`；更新总结到 docs/2_Architecture/13_bakeoff_findings.md。

指标：P50/P95 延迟、Recall@K、nDCG、索引时长、内存/磁盘占用、带过滤查询性能。

## 接入真实技术栈（最小实施步骤）

前置要求：已安装 Docker 与 docker-compose；Python 虚拟环境可用。

1) 启动依赖服务
- 使用 docker-compose 启动 Postgres+pgvector、Manticore（或替换为 OpenSearch/Qdrant 等）
  - 示例：
    - postgres:16 镜像，环境中启用 `shared_preload_libraries=vector`
    - manticoresearch/manticore:latest (可选)
- 运行：`docker-compose up -d`

2) 初始化数据库与索引
- 连接 Postgres，执行：
  - `CREATE EXTENSION IF NOT EXISTS vector;`
  - 建表：`CREATE TABLE docs (id text PRIMARY KEY, content text, embedding vector(384));`
  - 建索引：`CREATE INDEX ON docs USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);`
- Manticore（可选）：创建 RT 索引并写入文档。

3) 安装依赖并生成嵌入
- `pip install -r ../requirements.txt psycopg[binary] manticoresearch`
- 生成嵌入缓存（避免重复调用模型）：将 `data/corpus/` 编码后保存到 `data/embeddings/*.npy`

4) 写入与评测（按脚本职责拆分）
- 索引：`python scripts/index_pgvector.py` / `python scripts/index_manticore.py`
- 评估：`python scripts/eval_pgvector.py` / `python scripts/eval_manticore.py`
- 可使用环境变量配置：
  - `PG_DSN=postgresql://user:pass@localhost:5432/dbname`
  - `MANTICORE_HOST=localhost` `MANTICORE_PORT=9306`

5) 对齐本仓评测口径
- 输出统一到 `results/metrics.csv`，字段包含：`engine, query, top_k, retrieval_time_ms, precision_at_k, recall_at_k, ndcg_at_k`
- 保持与 `generate_summary.py` 的读取格式一致，便于自动汇总。
