#!/usr/bin/env python3
"""
E1 检索引擎对比实验
对比 pgvector vs Manticore Search 的性能和准确性
"""

import os
import time
import json
import csv
import logging
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
import numpy as np
from sentence_transformers import SentenceTransformer
import glob

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RetrievalResult:
    """检索结果"""
    doc_id: str
    content: str
    score: float
    retrieval_time_ms: float

@dataclass
class ExperimentMetrics:
    """实验指标"""
    engine: str
    query: str
    retrieval_time_ms: float
    total_docs: int
    returned_docs: int
    top_k: int
    precision_at_k: float = 0.0
    recall_at_k: float = 0.0
    ndcg_at_k: float = 0.0

class MockPgVectorEngine:
    """模拟 pgvector 检索引擎"""
    
    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(embedding_model)
        self.documents = []
        self.embeddings = []
        logger.info(f"初始化 PgVector 引擎，使用模型: {embedding_model}")
    
    def index_documents(self, documents: List[Dict[str, Any]]):
        """索引文档"""
        logger.info(f"PgVector 开始索引 {len(documents)} 个文档")
        start_time = time.time()
        
        self.documents = documents
        texts = [doc['content'] for doc in documents]
        self.embeddings = self.model.encode(texts)
        
        index_time = (time.time() - start_time) * 1000
        logger.info(f"PgVector 索引完成，耗时: {index_time:.2f}ms")
        return index_time
    
    def search(self, query: str, top_k: int = 10) -> Tuple[List[RetrievalResult], float]:
        """搜索文档"""
        start_time = time.time()
        
        # 编码查询
        query_embedding = self.model.encode([query])
        
        # 计算相似度
        similarities = np.dot(self.embeddings, query_embedding.T).flatten()
        
        # 获取top-k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        retrieval_time = (time.time() - start_time) * 1000
        
        results = []
        for idx in top_indices:
            results.append(RetrievalResult(
                doc_id=self.documents[idx]['id'],
                content=self.documents[idx]['content'],
                score=float(similarities[idx]),
                retrieval_time_ms=retrieval_time
            ))
        
        return results, retrieval_time

class MockManticoreEngine:
    """模拟 Manticore Search 检索引擎"""
    
    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2"):
        self.model = SentenceTransformer(embedding_model)
        self.documents = []
        self.embeddings = []
        logger.info(f"初始化 Manticore 引擎，使用模型: {embedding_model}")
    
    def index_documents(self, documents: List[Dict[str, Any]]):
        """索引文档"""
        logger.info(f"Manticore 开始索引 {len(documents)} 个文档")
        start_time = time.time()
        
        self.documents = documents
        texts = [doc['content'] for doc in documents]
        # 模拟 Manticore 的索引时间（通常更快）
        time.sleep(0.1)  # 模拟索引延迟
        self.embeddings = self.model.encode(texts)
        
        index_time = (time.time() - start_time) * 1000
        logger.info(f"Manticore 索引完成，耗时: {index_time:.2f}ms")
        return index_time
    
    def search(self, query: str, top_k: int = 10) -> Tuple[List[RetrievalResult], float]:
        """搜索文档"""
        start_time = time.time()
        
        # 编码查询
        query_embedding = self.model.encode([query])
        
        # 计算相似度（模拟 Manticore 的优化算法）
        similarities = np.dot(self.embeddings, query_embedding.T).flatten()
        
        # 模拟 Manticore 的查询优化（稍微快一些）
        retrieval_time = (time.time() - start_time) * 1000 * 0.8
        
        # 获取top-k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            results.append(RetrievalResult(
                doc_id=self.documents[idx]['id'],
                content=self.documents[idx]['content'],
                score=float(similarities[idx]),
                retrieval_time_ms=retrieval_time
            ))
        
        return results, retrieval_time

def load_real_dialogues(corpus_dir: str) -> List[Dict[str, Any]]:
    """从真实对话目录加载文档，每个文件作为一个文档。"""
    docs: List[Dict[str, Any]] = []
    paths = sorted(glob.glob(os.path.join(corpus_dir, '*.md')))
    for p in paths:
        try:
            with open(p, 'r', encoding='utf-8') as f:
                text = f.read()
            docs.append({
                'id': os.path.splitext(os.path.basename(p))[0],
                'content': text
            })
        except Exception:
            continue
    return docs

def generate_test_documents(num_docs: int = 1000) -> List[Dict[str, Any]]:
    """保留原合成数据生成（回退）"""
    logger.info(f"生成合成测试文档 {num_docs} 个（仅回退使用）")
    topics = [
        "机器学习算法原理和应用",
        "深度学习神经网络架构",
        "自然语言处理技术发展",
        "计算机视觉图像识别",
        "数据库系统设计优化",
        "分布式系统架构模式",
        "云计算服务部署策略",
        "网络安全防护机制",
        "软件工程开发流程",
        "人工智能伦理问题"
    ]
    documents = []
    for i in range(num_docs):
        topic = topics[i % len(topics)]
        content = f"这是关于{topic}的第{i+1}篇文档。文档内容包含了详细的技术说明、实现方法和最佳实践。"
        documents.append({'id': f'doc_{i+1}', 'content': content, 'topic': topic})
    return documents

def build_queries_from_dialogues(documents: List[Dict[str, Any]], max_per_doc: int = 5) -> List[str]:
    """基于真实对话内容构造查询：抽取适中长度句子作为查询。"""
    import re
    queries: List[str] = []
    for doc in documents:
        text = doc['content']
        # 粗略按句读切分
        sents = re.split(r'[。！？\n]', text)
        cands = [s.strip() for s in sents if 8 <= len(s.strip()) <= 40]
        for s in cands[:max_per_doc]:
            queries.append(s)
    # 限制总体数量
    return queries[:50] if len(queries) > 50 else queries

def calculate_metrics(results: List[RetrievalResult], relevant_docs: List[str], k: int) -> Dict[str, float]:
    """计算检索指标"""
    if not results:
        return {'precision_at_k': 0.0, 'recall_at_k': 0.0, 'ndcg_at_k': 0.0}
    
    # 简化的指标计算（实际应用中需要更复杂的相关性判断）
    retrieved_docs = [r.doc_id for r in results[:k]]
    relevant_retrieved = set(retrieved_docs) & set(relevant_docs)
    
    precision_at_k = len(relevant_retrieved) / min(len(retrieved_docs), k) if retrieved_docs else 0.0
    recall_at_k = len(relevant_retrieved) / len(relevant_docs) if relevant_docs else 0.0
    
    # 简化的 NDCG 计算
    dcg = sum(1.0 / np.log2(i + 2) for i, doc_id in enumerate(retrieved_docs[:k]) if doc_id in relevant_docs)
    idcg = sum(1.0 / np.log2(i + 2) for i in range(min(len(relevant_docs), k)))
    ndcg_at_k = dcg / idcg if idcg > 0 else 0.0
    
    return {
        'precision_at_k': precision_at_k,
        'recall_at_k': recall_at_k,
        'ndcg_at_k': ndcg_at_k
    }

def run_experiment(corpus_dir: str = None):
    """运行检索引擎对比实验"""
    logger.info("开始检索引擎对比实验")
    # 加载真实语料或回退到合成数据
    if corpus_dir and os.path.isdir(corpus_dir):
        documents = load_real_dialogues(corpus_dir)
        logger.info(f"从 {corpus_dir} 加载真实对话文档: {len(documents)}")
        queries = build_queries_from_dialogues(documents)
        if not queries:
            logger.warning("未能从真实语料构造查询，回退到内置查询")
            queries = ["这段对话的关键点是什么？", "这段内容的主要结论？", "作者的建议是什么？"]
    else:
        documents = generate_test_documents(1000)
        queries = [
            "机器学习算法有哪些？",
            "深度学习网络结构",
            "自然语言处理应用",
            "图像识别技术",
            "数据库优化方法",
            "分布式架构设计",
            "云服务部署",
            "网络安全防护",
            "软件开发流程",
            "AI伦理考虑"
        ]
    
    # 初始化引擎
    pgvector_engine = MockPgVectorEngine()
    manticore_engine = MockManticoreEngine()
    
    # 索引文档
    pgvector_index_time = pgvector_engine.index_documents(documents)
    manticore_index_time = manticore_engine.index_documents(documents)
    
    # 运行查询测试
    results = []
    top_k_values = [5, 10, 20]
    
    for query in queries:
        logger.info(f"测试查询: {query}")
        
        # 简化的相关文档判断（基于关键词匹配）
        relevant_docs = [doc['id'] for doc in documents if any(word in doc['content'] for word in query.split())]
        
        for top_k in top_k_values:
            # 测试 PgVector
            pgv_results, pgv_time = pgvector_engine.search(query, top_k)
            pgv_metrics = calculate_metrics(pgv_results, relevant_docs, top_k)
            
            results.append(ExperimentMetrics(
                engine='pgvector',
                query=query,
                retrieval_time_ms=pgv_time,
                total_docs=len(documents),
                returned_docs=len(pgv_results),
                top_k=top_k,
                **pgv_metrics
            ))
            
            # 测试 Manticore
            man_results, man_time = manticore_engine.search(query, top_k)
            man_metrics = calculate_metrics(man_results, relevant_docs, top_k)
            
            results.append(ExperimentMetrics(
                engine='manticore',
                query=query,
                retrieval_time_ms=man_time,
                total_docs=len(documents),
                returned_docs=len(man_results),
                top_k=top_k,
                **man_metrics
            ))
    
    # 保存结果
    save_results(results, pgvector_index_time, manticore_index_time)
    
    # 打印摘要
    print_summary(results)

def save_results(results: List[ExperimentMetrics], pgv_index_time: float, man_index_time: float):
    """保存实验结果到CSV"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    results_dir = os.path.join(script_dir, 'results')
    os.makedirs(results_dir, exist_ok=True)
    output_file = os.path.join(results_dir, 'experiment_results.csv')
    
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([
            'engine', 'query', 'retrieval_time_ms', 'total_docs', 'returned_docs', 
            'top_k', 'precision_at_k', 'recall_at_k', 'ndcg_at_k'
        ])
        
        for result in results:
            writer.writerow([
                result.engine, result.query, result.retrieval_time_ms,
                result.total_docs, result.returned_docs, result.top_k,
                result.precision_at_k, result.recall_at_k, result.ndcg_at_k
            ])
    
    # 保存索引时间
    with open(os.path.join(results_dir, 'index_times.json'), 'w') as f:
        json.dump({
            'pgvector_index_time_ms': pgv_index_time,
            'manticore_index_time_ms': man_index_time
        }, f, indent=2)
    
    logger.info(f"结果已保存到: {output_file}")

def print_summary(results: List[ExperimentMetrics]):
    """打印实验摘要"""
    print("\n" + "="*60)
    print("检索引擎对比实验摘要")
    print("="*60)
    
    # 按引擎分组统计
    pgv_results = [r for r in results if r.engine == 'pgvector']
    man_results = [r for r in results if r.engine == 'manticore']
    
    def calc_avg(metrics_list, attr):
        return sum(getattr(m, attr) for m in metrics_list) / len(metrics_list)
    
    print(f"\nPgVector 平均性能:")
    print(f"  检索时间: {calc_avg(pgv_results, 'retrieval_time_ms'):.2f}ms")
    print(f"  Precision@10: {calc_avg([r for r in pgv_results if r.top_k == 10], 'precision_at_k'):.3f}")
    print(f"  Recall@10: {calc_avg([r for r in pgv_results if r.top_k == 10], 'recall_at_k'):.3f}")
    print(f"  NDCG@10: {calc_avg([r for r in pgv_results if r.top_k == 10], 'ndcg_at_k'):.3f}")
    
    print(f"\nManticore 平均性能:")
    print(f"  检索时间: {calc_avg(man_results, 'retrieval_time_ms'):.2f}ms")
    print(f"  Precision@10: {calc_avg([r for r in man_results if r.top_k == 10], 'precision_at_k'):.3f}")
    print(f"  Recall@10: {calc_avg([r for r in man_results if r.top_k == 10], 'recall_at_k'):.3f}")
    print(f"  NDCG@10: {calc_avg([r for r in man_results if r.top_k == 10], 'ndcg_at_k'):.3f}")

if __name__ == "__main__":
    import sys
    corpus = sys.argv[1] if len(sys.argv) > 1 else None
    run_experiment(corpus)
