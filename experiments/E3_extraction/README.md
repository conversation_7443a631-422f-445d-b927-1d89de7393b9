# E3 结构化抽取 Bakeoff（langextract vs 规则化摘要）

目标：比较 langextract/LLM 与 规则化摘要在真实对话文本上的 Schema 通过率、字段覆盖与引用命中差异。

步骤：
1. 准备语料：优先使用 `experiments/real_dialogues/*.md`（也可自定义 `--corpus_dir`）。
2. 运行 `scripts/extract_langextract.py` 与 `scripts/extract_rule_based.py`，输出到 `results/`。
3. 评估：
   - 基于真实语料：`python experiments/scripts/eval_e3_real_extraction.py --corpus_dir experiments/real_dialogues`
   - 结果输出到 `experiments/E3_extraction/results/`
4. 汇总到 docs/2_Architecture/13_bakeoff_findings.md。

## 接入真实技术栈（最小实施步骤）

1) 保留规则法通道
- 根据当前规则实现补充可配置项（正则/关键词/停用词表），作为强基线。

2) 接入 LLM/抽取框架
- 二选一：
  - LangChain Extract：按 Schema 定义 Pydantic 模型，调用本地/云端 LLM；
  - 直接 OpenAI/Claude API：定义 JSON Schema 约束提示词，加上少量示例。
- 通过环境变量开关：`EXTRACTOR=rule|langextract|llm`

3) 数据与评测
- 输入：`data/turns.jsonl`，包含期望字段；
- 评测脚本：计算字段通过率、F1/EM（实体/关键点）、引用命中率、耗时与失败率；
- 输出：`results/experiment_results.csv` 与 `results/detailed_results.json`（保留现有格式）。

4) 成本控制
- 支持 dry-run 与采样比例（如 `SAMPLE_RATE=0.2`），避免大规模调用成本；
- 保留离线模拟路径，网络/凭证不可用时可回退。
