{"overall_summary": {"timestamp": "2025-08-22T02:03:13.478483", "experiments_completed": ["E1_retrieval_engine", "E3_extraction", "E2_text_splitting", "E4_orchestration", "E5_events_consistency"], "recommendations": ["Manticore 检索速度比 pgvector 快 96.9%", "两种检索引擎的准确性相当", "规则化方法与 LLM 成功率相当，但速度更快", "规则化方法比 LLM 快 100.0%", "LLM 的置信度评估更准确", "朴素分割检索召回率更优 2.8%", "语义分割跨标题污染率降低 1.8%", "朴素分割速度更快 83.0%", "本地状态机比 LangGraph Lite 更快 1.6%", "Outbox 一致性更好，延迟略高；适合关键事件"]}, "e1_results": {"pgvector_avg_time": 241.85276826222739, "manticore_avg_time": 7.558886210123698, "pgvector_avg_precision": 0.1, "manticore_avg_precision": 0.1, "total_queries": 10, "total_tests": 60, "pgvector_index_time_ms": 5022.4058628082275, "manticore_index_time_ms": 1040.9069061279297}, "e3_results": {"openai_llm_success_rate": 1.0, "rule_based_success_rate": 1.0, "openai_llm_avg_time": 7463.351202011108, "rule_based_avg_time": 0.3270149230957031, "openai_llm_avg_confidence": 0.8960000000000001, "rule_based_avg_confidence": 0.75, "total_texts": 5, "total_extractions": 10}, "e2_results": {"naive": {"avg_total_chunks": 1.0, "avg_chunk_size": 255.0, "avg_processing_time_ms": 0.021855036417643203, "avg_semantic_coherence": 0.6656565656565657, "avg_recall@5": 0.7460674157303371, "avg_ndcg@5": 0.632255715523541, "avg_top1_coverage": 0.8, "bleed_rate": 0.3827751196172249, "retrieval_total_chunks": 1254, "retrieval_avg_chunk_size": 449.31259968102074, "index_time_ms": 44586.04598045349}, "semantic": {"avg_total_chunks": 1.0, "avg_chunk_size": 190.0, "avg_processing_time_ms": 0.1281897226969401, "avg_semantic_coherence": 0.6656565656565657, "avg_recall@5": 0.7258426966292135, "avg_ndcg@5": 0.6237876016809918, "avg_top1_coverage": 0.7879278808466161, "bleed_rate": 0.3757178014766202, "retrieval_total_chunks": 1219, "retrieval_avg_chunk_size": 458.9811320754717, "index_time_ms": 41597.96977043152}}, "e4_results": {"langgraph_lite": {"success_rate": 1.0, "avg_total_time_ms": 339.9515151977539, "avg_memory_mb": 0.06510416666666667}, "local_state_machine": {"success_rate": 1.0, "avg_total_time_ms": 334.4947099685669, "avg_memory_mb": 0.0}}, "e5_results": {"outbox": {"success_rate": 1.0, "ordering_rate": 1.0, "avg_latency_ms": 9.168150160047743, "p99_latency_ms": 15.137275060017904}, "streams": {"success_rate": 1.0, "ordering_rate": 1.0, "avg_latency_ms": 71.10764238569472, "p99_latency_ms": 150.83845456441244}}}