# E4 轻量编排 Spike（Local vs LangGraph Lite）

目标：最小流程（意图 → 检索 → 回复）的双轨实现，对比开发效率与可观测性。

步骤：
1. Local：实现 `scripts/local_fsm.py`（Pydantic 状态、简单重试）。
2. LangGraph Lite：实现 `scripts/langgraph_flow.py`（2–3 节点、状态传递、日志）。
3. 记录实现时长、代码行数、错误处理难度、持久化复杂度、执行延迟，填入 `results/notes.md`。

## 接入真实技术栈（最小实施步骤）

测试数据：优先从 `experiments/real_dialogues/*.md` 中抽取用户发言片段（可用 `--corpus_dir` 指定），否则回退到内置样例。

运行：
- 使用真实对话：`python experiments/scripts/eval_e4_real_orchestration.py --corpus_dir experiments/real_dialogues`
- 使用内置：`python experiments/scripts/eval_e4_real_orchestration.py`

1) Local FSM → 生产化骨架
- 使用 Pydantic 定义 State，加入重试、超时、熔断；
- 引入结构化日志（logfmt/JSON），记录节点耗时与上下文大小。

2) LangGraph 最小接入
- 安装 `langgraph`，定义 3–4 个节点：`router`/`retrieval`/`generation`/`formatter`；
- 使用相同输入/工具集，运行两条路径并采集指标。

3) 可观测性与存储
- 将状态转移记录写入本地 sqlite（或简单 JSONL），便于重放；
- 指标采集写入 `results/metrics.csv`，字段已与汇总对齐。
