#!/usr/bin/env python3
"""
实验结果汇总脚本
生成所有实验的汇总CSV和可视化图表
"""

import os
import json
import csv
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Any

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_e1_results() -> Dict[str, Any]:
    """加载E1检索引擎实验结果"""
    results = {'exists': False, 'data': None, 'summary': {}}
    
    csv_file = 'E1_retrieval_engine/results/experiment_results.csv'
    json_file = 'E1_retrieval_engine/results/index_times.json'
    
    if os.path.exists(csv_file):
        results['exists'] = True
        df = pd.read_csv(csv_file)
        results['data'] = df
        
        # 计算摘要统计
        pgv_data = df[df['engine'] == 'pgvector']
        man_data = df[df['engine'] == 'manticore']
        
        results['summary'] = {
            'pgvector_avg_time': pgv_data['retrieval_time_ms'].mean(),
            'manticore_avg_time': man_data['retrieval_time_ms'].mean(),
            'pgvector_avg_precision': pgv_data[pgv_data['top_k'] == 10]['precision_at_k'].mean(),
            'manticore_avg_precision': man_data[man_data['top_k'] == 10]['precision_at_k'].mean(),
            'total_queries': len(df['query'].unique()),
            'total_tests': len(df)
        }
        
        # 加载索引时间
        if os.path.exists(json_file):
            with open(json_file, 'r') as f:
                index_times = json.load(f)
                results['summary'].update(index_times)
    
    return results

def load_e3_results() -> Dict[str, Any]:
    """加载E3结构化抽取实验结果"""
    results = {'exists': False, 'data': None, 'summary': {}}
    
    csv_file = 'E3_extraction/results/experiment_results.csv'
    json_file = 'E3_extraction/results/detailed_results.json'
    
    if os.path.exists(csv_file):
        results['exists'] = True
        df = pd.read_csv(csv_file)
        results['data'] = df
        
        # 计算摘要统计（支持新的方法名）
        # 检查是否有新的 openai_llm 方法
        if 'openai_llm' in df['method'].values:
            llm_data = df[df['method'] == 'openai_llm']
            rule_data = df[df['method'] == 'rule_based']

            results['summary'] = {
                'openai_llm_success_rate': llm_data['success'].mean(),
                'rule_based_success_rate': rule_data['success'].mean(),
                'openai_llm_avg_time': llm_data[llm_data['success']]['processing_time_ms'].mean(),
                'rule_based_avg_time': rule_data[rule_data['success']]['processing_time_ms'].mean(),
                'openai_llm_avg_confidence': llm_data['confidence_score'].mean(),
                'rule_based_avg_confidence': rule_data['confidence_score'].mean(),
                'total_texts': len(df['text_id'].unique()),
                'total_extractions': len(df)
            }
        else:
            # 回退到旧的 langextract 方法
            langextract_data = df[df['method'] == 'langextract']
            rule_data = df[df['method'] == 'rule_based']

            results['summary'] = {
                'langextract_success_rate': langextract_data['success'].mean(),
                'rule_based_success_rate': rule_data['success'].mean(),
                'langextract_avg_time': langextract_data[langextract_data['success']]['processing_time_ms'].mean(),
                'rule_based_avg_time': rule_data[rule_data['success']]['processing_time_ms'].mean(),
                'langextract_avg_confidence': langextract_data['confidence_score'].mean(),
                'rule_based_avg_confidence': rule_data['confidence_score'].mean(),
                'total_texts': len(df['text_id'].unique()),
                'total_extractions': len(df)
            }
    
    return results

def load_e2_results() -> Dict[str, Any]:
    """加载E2文本分割实验结果（包含新的检索评测）"""
    results = {'exists': False, 'data': None, 'summary': {}, 'retrieval_eval': None}

    # 加载旧的形态指标
    csv_file = 'E2_text_splitting/results/metrics.csv'
    if os.path.exists(csv_file):
        results['exists'] = True
        df = pd.read_csv(csv_file)
        results['data'] = df
        if not df.empty:
            grouped = df.groupby('method')
            summary = {}
            for method, g in grouped:
                summary[method] = {
                    'avg_total_chunks': g['total_chunks'].mean() if 'total_chunks' in g else None,
                    'avg_chunk_size': g['avg_chunk_size'].mean() if 'avg_chunk_size' in g else None,
                    'avg_processing_time_ms': g['processing_time_ms'].mean() if 'processing_time_ms' in g else None,
                    'avg_semantic_coherence': g['semantic_coherence_score'].mean() if 'semantic_coherence_score' in g else None,
                }
            results['summary'] = summary

    # 加载新的检索评测结果（优先向量版本）
    eval_summary_file_vector = 'E2_text_splitting/results/metrics_eval_summary_vector.json'
    eval_summary_file_ngram = 'E2_text_splitting/results/metrics_eval_summary.json'

    eval_summary_file = eval_summary_file_vector if os.path.exists(eval_summary_file_vector) else eval_summary_file_ngram

    if os.path.exists(eval_summary_file):
        results['exists'] = True  # 至少有检索评测结果
        with open(eval_summary_file, 'r', encoding='utf-8') as f:
            eval_data = json.load(f)
            results['retrieval_eval'] = eval_data

            # 将检索评测结果合并到 summary 中
            if not results['summary']:
                results['summary'] = {}

            # 添加检索质量指标
            for strategy in ['naive', 'semantic']:
                if strategy in eval_data:
                    strategy_data = eval_data[strategy]
                    if strategy not in results['summary']:
                        results['summary'][strategy] = {}

                    # 合并检索指标
                    results['summary'][strategy].update({
                        'avg_recall@5': strategy_data.get('avg_recall@5'),
                        'avg_ndcg@5': strategy_data.get('avg_ndcg@5'),
                        'avg_top1_coverage': strategy_data.get('avg_top1_coverage'),
                        'bleed_rate': strategy_data.get('bleed_rate'),
                        'retrieval_total_chunks': strategy_data.get('total_chunks'),
                        'retrieval_avg_chunk_size': strategy_data.get('avg_chunk_size'),
                        'index_time_ms': strategy_data.get('index_time_ms')
                    })

    return results

def load_e4_results() -> Dict[str, Any]:
    """加载E4编排对比实验结果"""
    results = {'exists': False, 'data': None, 'summary': {}}
    csv_file_std = 'E4_orchestration/results/metrics.csv'
    csv_file_legacy = 'E4_orchestration/results/orchestration_metrics.csv'
    csv_file = csv_file_std if os.path.exists(csv_file_std) else csv_file_legacy
    if os.path.exists(csv_file):
        results['exists'] = True
        df = pd.read_csv(csv_file)
        results['data'] = df
        if not df.empty:
            grouped = df.groupby('orchestrator')
            summary = {}
            for orch, g in grouped:
                summary[orch] = {
                    'success_rate': g['success'].mean() if 'success' in g else None,
                    'avg_total_time_ms': g['total_time_ms'].mean() if 'total_time_ms' in g else None,
                    'avg_memory_mb': g['memory_usage_mb'].mean() if 'memory_usage_mb' in g else None,
                }
            results['summary'] = summary
    return results

def load_e5_results() -> Dict[str, Any]:
    """加载E5事件一致性实验结果（最小汇总）"""
    results = {'exists': False, 'data': None, 'summary': {}}
    csv_file = 'E5_events_consistency/results/metrics.csv'
    if os.path.exists(csv_file):
        import pandas as pd  # 已在顶部导入
        df = pd.read_csv(csv_file)
        if not df.empty:
            results['exists'] = True
            results['data'] = df
            grouped = df.groupby('approach').agg({
                'success_rate': 'mean',
                'ordering_rate': 'mean',
                'avg_latency_ms': 'mean',
                'p99_latency_ms': 'mean',
            })
            results['summary'] = grouped.to_dict(orient='index')
    return results

def generate_overall_summary(e1_results: Dict, e3_results: Dict, e2_results: Dict, e4_results: Dict, e5_results: Dict) -> Dict[str, Any]:
    """生成总体摘要"""
    summary = {
        'timestamp': datetime.now().isoformat(),
        'experiments_completed': [],
        'recommendations': []
    }
    
    # E1 检索引擎分析
    if e1_results['exists']:
        summary['experiments_completed'].append('E1_retrieval_engine')
        e1_summary = e1_results['summary']
        
        # 性能对比
        if e1_summary['manticore_avg_time'] < e1_summary['pgvector_avg_time']:
            speed_improvement = (e1_summary['pgvector_avg_time'] - e1_summary['manticore_avg_time']) / e1_summary['pgvector_avg_time'] * 100
            summary['recommendations'].append(f"Manticore 检索速度比 pgvector 快 {speed_improvement:.1f}%")
        
        # 准确性对比
        precision_diff = e1_summary['manticore_avg_precision'] - e1_summary['pgvector_avg_precision']
        if abs(precision_diff) < 0.05:
            summary['recommendations'].append("两种检索引擎的准确性相当")
        elif precision_diff > 0:
            summary['recommendations'].append("Manticore 的检索准确性略高")
        else:
            summary['recommendations'].append("pgvector 的检索准确性略高")
    
    # E3 结构化抽取分析
    if e3_results['exists']:
        summary['experiments_completed'].append('E3_extraction')
        e3_summary = e3_results['summary']

        # 检查是否有新的 openai_llm 结果
        if 'openai_llm_success_rate' in e3_summary:
            # 可靠性对比
            rule_success = e3_summary.get('rule_based_success_rate', 0)
            llm_success = e3_summary.get('openai_llm_success_rate', 0)
            if rule_success >= llm_success:
                summary['recommendations'].append("规则化方法与 LLM 成功率相当，但速度更快")

            # 性能对比
            rule_time = e3_summary.get('rule_based_avg_time', 0)
            llm_time = e3_summary.get('openai_llm_avg_time', 0)
            if rule_time < llm_time and llm_time > 0:
                speed_improvement = (llm_time - rule_time) / llm_time * 100
                summary['recommendations'].append(f"规则化方法比 LLM 快 {speed_improvement:.1f}%")

            # 置信度对比
            rule_conf = e3_summary.get('rule_based_avg_confidence', 0)
            llm_conf = e3_summary.get('openai_llm_avg_confidence', 0)
            if llm_conf > rule_conf:
                summary['recommendations'].append("LLM 的置信度评估更准确")
        else:
            # 回退到旧的 langextract 逻辑
            if e3_summary.get('rule_based_success_rate', 0) > e3_summary.get('langextract_success_rate', 0):
                summary['recommendations'].append("规则化方法的成功率更高，更可靠")

            rule_time = e3_summary.get('rule_based_avg_time', 0)
            lang_time = e3_summary.get('langextract_avg_time', 0)
            if rule_time < lang_time and lang_time > 0:
                speed_improvement = (lang_time - rule_time) / lang_time * 100
                summary['recommendations'].append(f"规则化方法比 langextract 快 {speed_improvement:.1f}%")

            if e3_summary.get('langextract_avg_confidence', 0) > e3_summary.get('rule_based_avg_confidence', 0):
                summary['recommendations'].append("langextract 的置信度评估更准确")
    
    # E2 文本分割分析（包含检索评测）
    if 'load_e2_results' in globals():
        if e2_results.get('exists') and e2_results.get('summary'):
            summary['experiments_completed'].append('E2_text_splitting')
            s = e2_results['summary']

            # 检索质量对比
            if 'semantic' in s and 'naive' in s:
                naive_recall = s['naive'].get('avg_recall@5', 0)
                semantic_recall = s['semantic'].get('avg_recall@5', 0)
                naive_ndcg = s['naive'].get('avg_ndcg@5', 0)
                semantic_ndcg = s['semantic'].get('avg_ndcg@5', 0)
                naive_bleed = s['naive'].get('bleed_rate', 0)
                semantic_bleed = s['semantic'].get('bleed_rate', 0)

                # 检索效果对比
                if semantic_recall > naive_recall:
                    improvement = (semantic_recall - naive_recall) / max(naive_recall, 1e-6) * 100
                    if improvement >= 10:
                        summary['recommendations'].append(f'语义分割检索召回率提升 {improvement:.1f}%，建议采用')
                    else:
                        summary['recommendations'].append(f'语义分割检索召回率略优 {improvement:.1f}%')
                elif naive_recall > semantic_recall:
                    degradation = (naive_recall - semantic_recall) / max(semantic_recall, 1e-6) * 100
                    summary['recommendations'].append(f'朴素分割检索召回率更优 {degradation:.1f}%')
                else:
                    summary['recommendations'].append('两种分割策略的检索召回率相当')

                # 跨标题污染率对比
                if semantic_bleed < naive_bleed:
                    improvement = (naive_bleed - semantic_bleed) / max(naive_bleed, 1e-6) * 100
                    summary['recommendations'].append(f'语义分割跨标题污染率降低 {improvement:.1f}%')

                # 传统指标对比
                if s['semantic'].get('avg_semantic_coherence', 0) > s['naive'].get('avg_semantic_coherence', 0):
                    summary['recommendations'].append('语义分割的语义连贯性更好')
                if s['naive'].get('avg_processing_time_ms', 0) < s['semantic'].get('avg_processing_time_ms', 0):
                    speed_impr = (s['semantic']['avg_processing_time_ms'] - s['naive']['avg_processing_time_ms']) / max(s['semantic']['avg_processing_time_ms'], 1e-6) * 100
                    summary['recommendations'].append(f"朴素分割速度更快 {speed_impr:.1f}%")
    # E4 编排分析
    if 'load_e4_results' in globals():
        if e4_results.get('exists') and e4_results.get('summary'):
            summary['experiments_completed'].append('E4_orchestration')
            s = e4_results['summary']
            if 'local_state_machine' in s and 'langgraph_lite' in s:
                if s['local_state_machine'].get('avg_total_time_ms', 0) < s['langgraph_lite'].get('avg_total_time_ms', 0):
                    diff = (s['langgraph_lite']['avg_total_time_ms'] - s['local_state_machine']['avg_total_time_ms']) / max(s['local_state_machine']['avg_total_time_ms'], 1e-6) * 100
                    summary['recommendations'].append(f"本地状态机比 LangGraph Lite 更快 {diff:.1f}%")
    # E5 事件一致性分析（只做一句话建议）
    if e5_results.get('exists') and e5_results.get('summary'):
        summary['experiments_completed'].append('E5_events_consistency')
        s = e5_results['summary']
        if 'streams' in s and 'outbox' in s:
            if s['outbox'].get('success_rate', 0) >= s['streams'].get('success_rate', 0):
                summary['recommendations'].append('Outbox 一致性更好，延迟略高；适合关键事件')
            else:
                summary['recommendations'].append('Streams 成功率更高（异常），请复核参数')
    return summary

def create_visualizations(e1_results: Dict, e3_results: Dict, e2_results: Dict = None, e4_results: Dict = None, e5_results: Dict = None):
    """创建可视化图表"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('技术对比实验结果可视化', fontsize=16)
    
    # E1: 检索时间对比
    if e1_results['exists']:
        df = e1_results['data']
        avg_times = df.groupby('engine')['retrieval_time_ms'].mean()
        
        axes[0, 0].bar(avg_times.index, avg_times.values, color=['skyblue', 'lightcoral'])
        axes[0, 0].set_title('E1: 平均检索时间对比')
        axes[0, 0].set_ylabel('时间 (ms)')
        
        # E1: 准确性对比
        precision_data = df[df['top_k'] == 10].groupby('engine')['precision_at_k'].mean()
        axes[0, 1].bar(precision_data.index, precision_data.values, color=['lightgreen', 'orange'])
        axes[0, 1].set_title('E1: Precision@10 对比')
        axes[0, 1].set_ylabel('Precision')
        axes[0, 1].set_ylim(0, 1)
    else:
        axes[0, 0].text(0.5, 0.5, 'E1 实验未完成', ha='center', va='center', transform=axes[0, 0].transAxes)
        axes[0, 1].text(0.5, 0.5, 'E1 实验未完成', ha='center', va='center', transform=axes[0, 1].transAxes)
    
    # E3: 处理时间对比
    if e3_results['exists']:
        df = e3_results['data']
        successful_data = df[df['success'] == True]
        avg_times = successful_data.groupby('method')['processing_time_ms'].mean()
        
        axes[1, 0].bar(avg_times.index, avg_times.values, color=['mediumpurple', 'gold'])
        axes[1, 0].set_title('E3: 平均处理时间对比')
        axes[1, 0].set_ylabel('时间 (ms)')
        
        # E3: 成功率对比
        success_rates = df.groupby('method')['success'].mean()
        axes[1, 1].bar(success_rates.index, success_rates.values, color=['lightblue', 'lightpink'])
        axes[1, 1].set_title('E3: 成功率对比')
        axes[1, 1].set_ylabel('成功率')
        axes[1, 1].set_ylim(0, 1)
    else:
        axes[1, 0].text(0.5, 0.5, 'E3 实验未完成', ha='center', va='center', transform=axes[1, 0].transAxes)
        axes[1, 1].text(0.5, 0.5, 'E3 实验未完成', ha='center', va='center', transform=axes[1, 1].transAxes)

    # 如果 E2 可用，用其覆盖下行图；优先展示检索评测结果
    if e2_results and e2_results.get('exists'):
        try:
            # 优先展示检索评测结果
            if e2_results.get('summary') and 'naive' in e2_results['summary'] and 'semantic' in e2_results['summary']:
                s = e2_results['summary']

                # 检索召回率对比
                if s['naive'].get('avg_recall@5') is not None and s['semantic'].get('avg_recall@5') is not None:
                    axes[1, 0].clear()
                    methods = ['naive', 'semantic']
                    recalls = [s[m]['avg_recall@5'] for m in methods]
                    axes[1, 0].bar(methods, recalls, color=['#ff9800', '#4caf50'])
                    axes[1, 0].set_title('E2: 检索召回率@5 对比')
                    axes[1, 0].set_ylabel('Recall@5')
                    axes[1, 0].set_ylim(0, 1)

                # 跨标题污染率对比
                if s['naive'].get('bleed_rate') is not None and s['semantic'].get('bleed_rate') is not None:
                    axes[1, 1].clear()
                    methods = ['naive', 'semantic']
                    bleeds = [s[m]['bleed_rate'] for m in methods]
                    axes[1, 1].bar(methods, bleeds, color=['#f44336', '#2196f3'])
                    axes[1, 1].set_title('E2: 跨标题污染率对比')
                    axes[1, 1].set_ylabel('污染率')
                    axes[1, 1].set_ylim(0, max(bleeds) * 1.2 if bleeds else 1)

            # 如果没有检索评测结果，回退到传统指标
            elif e2_results.get('data') is not None:
                df2 = e2_results['data']
                if {'method','semantic_coherence_score'}.issubset(df2.columns):
                    axes[1, 0].clear()
                    avg_coh = df2.groupby('method')['semantic_coherence_score'].mean()
                    axes[1, 0].bar(avg_coh.index, avg_coh.values, color=['#7cb342', '#5e35b1'][:len(avg_coh)])
                    axes[1, 0].set_title('E2: 平均语义连贯性')
                    axes[1, 0].set_ylabel('coherence')
                if {'method','processing_time_ms'}.issubset(df2.columns):
                    axes[1, 1].clear()
                    avg_time = df2.groupby('method')['processing_time_ms'].mean()
                    axes[1, 1].bar(avg_time.index, avg_time.values, color=['#90caf9', '#f48fb1'][:len(avg_time)])
                    axes[1, 1].set_title('E2: 平均处理时间(ms)')
                    axes[1, 1].set_ylabel('ms')
        except Exception:
            pass
    
    plt.tight_layout()
    plt.savefig('experiment_summary.png', dpi=300, bbox_inches='tight')
    plt.close()

def save_summary_csv(e1_results: Dict, e3_results: Dict, overall_summary: Dict, e2_results: Dict = None, e4_results: Dict = None, e5_results: Dict = None):
    """保存汇总CSV"""
    summary_data = []
    
    # E1 结果
    if e1_results['exists']:
        e1_summary = e1_results['summary']
        summary_data.extend([
            ['E1_retrieval_engine', 'pgvector_avg_time_ms', e1_summary['pgvector_avg_time']],
            ['E1_retrieval_engine', 'manticore_avg_time_ms', e1_summary['manticore_avg_time']],
            ['E1_retrieval_engine', 'pgvector_avg_precision', e1_summary['pgvector_avg_precision']],
            ['E1_retrieval_engine', 'manticore_avg_precision', e1_summary['manticore_avg_precision']],
            ['E1_retrieval_engine', 'total_queries', e1_summary['total_queries']],
            ['E1_retrieval_engine', 'pgvector_index_time_ms', e1_summary.get('pgvector_index_time_ms', 0)],
            ['E1_retrieval_engine', 'manticore_index_time_ms', e1_summary.get('manticore_index_time_ms', 0)]
        ])
    
    # E3 结果（支持新旧格式）
    if e3_results['exists']:
        e3_summary = e3_results['summary']

        # 检查是否有新的 openai_llm 结果
        if 'openai_llm_success_rate' in e3_summary:
            summary_data.extend([
                ['E3_extraction', 'openai_llm_success_rate', e3_summary['openai_llm_success_rate']],
                ['E3_extraction', 'rule_based_success_rate', e3_summary['rule_based_success_rate']],
                ['E3_extraction', 'openai_llm_avg_time_ms', e3_summary['openai_llm_avg_time']],
                ['E3_extraction', 'rule_based_avg_time_ms', e3_summary['rule_based_avg_time']],
                ['E3_extraction', 'openai_llm_avg_confidence', e3_summary['openai_llm_avg_confidence']],
                ['E3_extraction', 'rule_based_avg_confidence', e3_summary['rule_based_avg_confidence']],
                ['E3_extraction', 'total_texts', e3_summary['total_texts']]
            ])
        else:
            # 回退到旧格式
            summary_data.extend([
                ['E3_extraction', 'langextract_success_rate', e3_summary.get('langextract_success_rate', 0)],
                ['E3_extraction', 'rule_based_success_rate', e3_summary.get('rule_based_success_rate', 0)],
                ['E3_extraction', 'langextract_avg_time_ms', e3_summary.get('langextract_avg_time', 0)],
                ['E3_extraction', 'rule_based_avg_time_ms', e3_summary.get('rule_based_avg_time', 0)],
                ['E3_extraction', 'langextract_avg_confidence', e3_summary.get('langextract_avg_confidence', 0)],
                ['E3_extraction', 'rule_based_avg_confidence', e3_summary.get('rule_based_avg_confidence', 0)],
                ['E3_extraction', 'total_texts', e3_summary.get('total_texts', 0)]
            ])
    
    # E2 结果（包含检索评测指标）
    if e2_results and e2_results.get('exists') and e2_results.get('summary'):
        s = e2_results['summary']
        for method, vals in s.items():
            summary_data.extend([
                ['E2_text_splitting', f'{method}_avg_total_chunks', vals.get('avg_total_chunks')],
                ['E2_text_splitting', f'{method}_avg_chunk_size', vals.get('avg_chunk_size')],
                ['E2_text_splitting', f'{method}_avg_processing_time_ms', vals.get('avg_processing_time_ms')],
                ['E2_text_splitting', f'{method}_avg_semantic_coherence', vals.get('avg_semantic_coherence')],
                # 新增检索评测指标
                ['E2_text_splitting', f'{method}_avg_recall@5', vals.get('avg_recall@5')],
                ['E2_text_splitting', f'{method}_avg_ndcg@5', vals.get('avg_ndcg@5')],
                ['E2_text_splitting', f'{method}_avg_top1_coverage', vals.get('avg_top1_coverage')],
                ['E2_text_splitting', f'{method}_bleed_rate', vals.get('bleed_rate')],
                ['E2_text_splitting', f'{method}_retrieval_total_chunks', vals.get('retrieval_total_chunks')],
                ['E2_text_splitting', f'{method}_index_time_ms', vals.get('index_time_ms')],
            ])

    # E4 结果
    if e4_results and e4_results.get('exists') and e4_results.get('summary'):
        s = e4_results['summary']
        for orch, vals in s.items():
            summary_data.extend([
                ['E4_orchestration', f'{orch}_success_rate', vals.get('success_rate')],
                ['E4_orchestration', f'{orch}_avg_total_time_ms', vals.get('avg_total_time_ms')],
                ['E4_orchestration', f'{orch}_avg_memory_mb', vals.get('avg_memory_mb')],
            ])

    # E5 结果
    if e5_results and e5_results.get('exists') and e5_results.get('summary'):
        s = e5_results['summary']
        for approach, vals in s.items():
            summary_data.extend([
                ['E5_events_consistency', f'{approach}_success_rate', vals.get('success_rate')],
                ['E5_events_consistency', f'{approach}_ordering_rate', vals.get('ordering_rate')],
                ['E5_events_consistency', f'{approach}_avg_latency_ms', vals.get('avg_latency_ms')],
                ['E5_events_consistency', f'{approach}_p99_latency_ms', vals.get('p99_latency_ms')],
            ])

    # 保存CSV
    with open('experiment_summary.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['experiment', 'metric', 'value'])
        writer.writerows(summary_data)
    
    # 保存详细摘要
    with open('experiment_summary.json', 'w', encoding='utf-8') as f:
        json.dump({
            'overall_summary': overall_summary,
            'e1_results': e1_results['summary'] if e1_results['exists'] else {},
            'e3_results': e3_results['summary'] if e3_results['exists'] else {},
            'e2_results': (e2_results['summary'] if (e2_results and e2_results.get('exists')) else {}),
            'e4_results': (e4_results['summary'] if (e4_results and e4_results.get('exists')) else {}),
            'e5_results': (e5_results['summary'] if (e5_results and e5_results.get('exists')) else {}),
        }, f, indent=2, ensure_ascii=False)

def print_console_summary(overall_summary: Dict):
    """打印控制台摘要"""
    print("\n" + "="*60)
    print("实验结果汇总")
    print("="*60)
    print(f"生成时间: {overall_summary['timestamp']}")
    print(f"已完成实验: {', '.join(overall_summary['experiments_completed'])}")
    
    print("\n关键发现:")
    for i, rec in enumerate(overall_summary['recommendations'], 1):
        print(f"  {i}. {rec}")
    
    print(f"\n输出文件:")
    print(f"  - experiment_summary.csv (汇总数据)")
    print(f"  - experiment_summary.json (详细结果)")
    print(f"  - experiment_summary.png (可视化图表)")
    print("="*60)

def main():
    """主函数"""
    print("开始生成实验结果汇总...")
    
    # 加载实验结果
    e1_results = load_e1_results()
    e3_results = load_e3_results()
    # 加载 E2/E4/E5（即使缺失也返回 exists=False）
    e2_results = load_e2_results()
    e4_results = load_e4_results()
    e5_results = load_e5_results()
    
    # 生成总体摘要
    overall_summary = generate_overall_summary(e1_results, e3_results, e2_results, e4_results, e5_results)
    
    # 创建可视化
    try:
        create_visualizations(e1_results, e3_results, e2_results, e4_results, e5_results)
        print("可视化图表已生成: experiment_summary.png")
    except Exception as e:
        print(f"可视化生成失败: {e}")
    
    # 保存汇总结果
    save_summary_csv(e1_results, e3_results, overall_summary, e2_results, e4_results, e5_results)
    print("汇总数据已保存: experiment_summary.csv")
    
    # 打印控制台摘要
    print_console_summary(overall_summary)

if __name__ == "__main__":
    main()
